{include file="$template/header.tpl"}

<div class="container mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- <PERSON><PERSON> Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-white mb-2">
                    <i data-lucide="shopping-cart" class="w-8 h-8 mr-3 inline text-blue-400"></i>
                    Shopping Cart
                </h1>
                <p class="text-gray-300">Review your order and proceed to checkout</p>
            </div>
            <div class="hidden md:block">
                <div class="flex items-center space-x-4 text-sm text-gray-400">
                    <div class="flex items-center">
                        <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white font-semibold mr-2">1</div>
                        <span class="text-blue-400 font-medium">Review Cart</span>
                    </div>
                    <i data-lucide="chevron-right" class="w-4 h-4"></i>
                    <div class="flex items-center">
                        <div class="w-8 h-8 bg-slate-700 rounded-full flex items-center justify-center text-gray-400 font-semibold mr-2">2</div>
                        <span>Checkout</span>
                    </div>
                    <i data-lucide="chevron-right" class="w-4 h-4"></i>
                    <div class="flex items-center">
                        <div class="w-8 h-8 bg-slate-700 rounded-full flex items-center justify-center text-gray-400 font-semibold mr-2">3</div>
                        <span>Complete</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    {if $errormessage}
        <div class="alert alert-danger mb-6">
            <i data-lucide="alert-circle" class="w-4 h-4 mr-2 inline"></i>
            {$errormessage}
        </div>
    {/if}

    {if $promotioncode && $rawdiscount}
        <div class="alert alert-success mb-6">
            <i data-lucide="tag" class="w-4 h-4 mr-2 inline"></i>
            {$LANG.promoappliedsuccessfully}
        </div>
    {/if}

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Cart Items -->
        <div class="lg:col-span-2">
            {if $products}
                <div class="panel">
                    <div class="panel-heading">
                        <h3 class="flex items-center">
                            <i data-lucide="package" class="w-5 h-5 mr-2 text-blue-400"></i>
                            Cart Items ({$totalitems})
                        </h3>
                    </div>
                    <div class="panel-body p-0">
                        {foreach from=$products item=product}
                            <div class="border-b border-slate-700/50 last:border-b-0 p-6">
                                <div class="flex items-start justify-between">
                                    <div class="flex-1">
                                        <div class="flex items-center mb-2">
                                            <div class="w-12 h-12 bg-gradient-to-r from-blue-500/20 to-purple-500/20 rounded-lg flex items-center justify-center mr-4">
                                                <i data-lucide="server" class="w-6 h-6 text-blue-400"></i>
                                            </div>
                                            <div>
                                                <h4 class="font-semibold text-white text-lg">{$product.productinfo.name}</h4>
                                                <p class="text-gray-400 text-sm">{$product.productinfo.description}</p>
                                            </div>
                                        </div>
                                        
                                        {if $product.domain}
                                            <div class="ml-16 mb-3">
                                                <div class="bg-slate-800/50 rounded-lg p-3">
                                                    <div class="flex items-center">
                                                        <i data-lucide="globe" class="w-4 h-4 text-green-400 mr-2"></i>
                                                        <span class="text-white font-medium">{$product.domain}</span>
                                                        <span class="ml-2 text-sm text-gray-400">({$product.billingcycle})</span>
                                                    </div>
                                                </div>
                                            </div>
                                        {/if}

                                        {if $product.configoptions}
                                            <div class="ml-16 space-y-2">
                                                {foreach from=$product.configoptions item=configoption}
                                                    <div class="flex items-center justify-between text-sm">
                                                        <span class="text-gray-300">{$configoption.name}:</span>
                                                        <span class="text-white font-medium">{$configoption.option}</span>
                                                    </div>
                                                {/foreach}
                                            </div>
                                        {/if}
                                    </div>
                                    
                                    <div class="text-right ml-4">
                                        <div class="text-2xl font-bold text-white mb-1">
                                            {$product.pricing.totaltoday}
                                        </div>
                                        <div class="text-sm text-gray-400">
                                            {$product.billingcycle}
                                        </div>
                                        {if $product.pricing.totalrecurring}
                                            <div class="text-sm text-gray-400">
                                                Then {$product.pricing.totalrecurring}
                                            </div>
                                        {/if}
                                        <div class="mt-3">
                                            <a href="{$WEB_ROOT}/cart.php?a=remove&i={$product.key}" 
                                               class="text-red-400 hover:text-red-300 text-sm transition-colors duration-300">
                                                <i data-lucide="trash-2" class="w-4 h-4 mr-1 inline"></i>
                                                Remove
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        {/foreach}
                    </div>
                </div>

                <!-- Addons -->
                {if $addons}
                    <div class="panel mt-6">
                        <div class="panel-heading">
                            <h3 class="flex items-center">
                                <i data-lucide="plus-circle" class="w-5 h-5 mr-2 text-purple-400"></i>
                                Add-ons
                            </h3>
                        </div>
                        <div class="panel-body p-0">
                            {foreach from=$addons item=addon}
                                <div class="border-b border-slate-700/50 last:border-b-0 p-6">
                                    <div class="flex items-center justify-between">
                                        <div class="flex items-center">
                                            <div class="w-10 h-10 bg-gradient-to-r from-purple-500/20 to-pink-500/20 rounded-lg flex items-center justify-center mr-4">
                                                <i data-lucide="plus" class="w-5 h-5 text-purple-400"></i>
                                            </div>
                                            <div>
                                                <h4 class="font-semibold text-white">{$addon.name}</h4>
                                                <p class="text-gray-400 text-sm">{$addon.description}</p>
                                            </div>
                                        </div>
                                        <div class="text-right">
                                            <div class="text-lg font-bold text-white">
                                                {$addon.recurringamount}
                                            </div>
                                            <div class="text-sm text-gray-400">
                                                {$addon.billingcycle}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            {/foreach}
                        </div>
                    </div>
                {/if}

                <!-- Continue Shopping -->
                <div class="mt-6">
                    <a href="{$WEB_ROOT}/cart.php?gid={$gid}" class="btn btn-secondary">
                        <i data-lucide="arrow-left" class="w-4 h-4 mr-2"></i>
                        Continue Shopping
                    </a>
                </div>

            {else}
                <!-- Empty Cart -->
                <div class="panel">
                    <div class="panel-body text-center py-16">
                        <div class="w-24 h-24 bg-slate-800/50 rounded-full flex items-center justify-center mx-auto mb-6">
                            <i data-lucide="shopping-cart" class="w-12 h-12 text-gray-400"></i>
                        </div>
                        <h3 class="text-2xl font-semibold text-white mb-4">Your Cart is Empty</h3>
                        <p class="text-gray-400 mb-8 max-w-md mx-auto">
                            Browse our hosting solutions and add services to your cart to get started.
                        </p>
                        <div class="flex flex-col sm:flex-row gap-4 justify-center">
                            <a href="{$WEB_ROOT}/cart.php?gid=1" class="btn btn-primary">
                                <i data-lucide="server" class="w-4 h-4 mr-2"></i>
                                VPS Hosting
                            </a>
                            <a href="{$WEB_ROOT}/cart.php?gid=3" class="btn btn-secondary">
                                <i data-lucide="database" class="w-4 h-4 mr-2"></i>
                                Dedicated Servers
                            </a>
                        </div>
                    </div>
                </div>
            {/if}
        </div>

        <!-- Order Summary -->
        {if $products}
            <div class="lg:col-span-1">
                <div class="panel sticky top-24">
                    <div class="panel-heading">
                        <h3 class="flex items-center">
                            <i data-lucide="calculator" class="w-5 h-5 mr-2 text-green-400"></i>
                            Order Summary
                        </h3>
                    </div>
                    <div class="panel-body">
                        <!-- Pricing Breakdown -->
                        <div class="space-y-3 mb-6">
                            <div class="flex justify-between text-gray-300">
                                <span>Subtotal:</span>
                                <span>{$subtotal}</span>
                            </div>
                            
                            {if $promotioncode && $rawdiscount}
                                <div class="flex justify-between text-green-400">
                                    <span>Discount ({$promotioncode}):</span>
                                    <span>-{$discount}</span>
                                </div>
                            {/if}
                            
                            {if $taxrate}
                                <div class="flex justify-between text-gray-300">
                                    <span>Tax ({$taxrate}%):</span>
                                    <span>{$taxtotal}</span>
                                </div>
                            {/if}
                            
                            <div class="border-t border-slate-700 pt-3">
                                <div class="flex justify-between text-xl font-bold text-white">
                                    <span>Total:</span>
                                    <span>{$total}</span>
                                </div>
                                {if $totalrecurringmonthly}
                                    <div class="text-sm text-gray-400 mt-1">
                                        Then {$totalrecurringmonthly}/month
                                    </div>
                                {/if}
                            </div>
                        </div>

                        <!-- Promotion Code -->
                        {if !$promotioncode}
                            <div class="mb-6">
                                <form method="post" action="{$smarty.server.PHP_SELF}">
                                    <div class="form-group">
                                        <label class="form-label text-sm">
                                            <i data-lucide="tag" class="w-4 h-4 mr-1 inline"></i>
                                            Promotion Code
                                        </label>
                                        <div class="flex">
                                            <input type="text" 
                                                   name="promocode" 
                                                   class="form-control rounded-r-none" 
                                                   placeholder="Enter code">
                                            <button type="submit" 
                                                    name="validatepromo" 
                                                    class="btn btn-secondary rounded-l-none border-l-0">
                                                Apply
                                            </button>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        {/if}

                        <!-- Checkout Button -->
                        <div class="space-y-3">
                            <a href="{$WEB_ROOT}/cart.php?a=checkout" class="btn btn-primary w-full">
                                <i data-lucide="credit-card" class="w-4 h-4 mr-2"></i>
                                Proceed to Checkout
                            </a>
                            
                            <div class="text-center">
                                <div class="flex items-center justify-center space-x-2 text-sm text-gray-400">
                                    <i data-lucide="shield-check" class="w-4 h-4 text-green-400"></i>
                                    <span>Secure SSL Checkout</span>
                                </div>
                            </div>
                        </div>

                        <!-- Trust Badges -->
                        <div class="mt-6 pt-6 border-t border-slate-700">
                            <div class="text-center space-y-3">
                                <div class="flex items-center justify-center space-x-4">
                                    <div class="flex items-center text-xs text-gray-400">
                                        <i data-lucide="clock" class="w-3 h-3 mr-1"></i>
                                        <span>Instant Setup</span>
                                    </div>
                                    <div class="flex items-center text-xs text-gray-400">
                                        <i data-lucide="headphones" class="w-3 h-3 mr-1"></i>
                                        <span>24/7 Support</span>
                                    </div>
                                </div>
                                <div class="flex items-center justify-center text-xs text-gray-400">
                                    <i data-lucide="award" class="w-3 h-3 mr-1"></i>
                                    <span>99.9% Uptime Guarantee</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        {/if}
    </div>
</div>

{include file="$template/footer.tpl"}
