{include file="$template/header.tpl"}

<div class="container mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Invoice Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-white mb-2">
                    <i data-lucide="file-text" class="w-8 h-8 mr-3 inline text-blue-400"></i>
                    Invoice #{$invoicenum}
                </h1>
                <p class="text-gray-300">
                    Invoice Date: {$date} | Due Date: {$datedue}
                </p>
            </div>
            <div class="flex items-center space-x-4">
                <span class="badge badge-{$status|lower} text-lg px-4 py-2">
                    {$status}
                </span>
                {if $status eq "Unpaid"}
                    <a href="{$WEB_ROOT}/viewinvoice.php?id={$invoiceid}&paynow=true" class="btn btn-primary">
                        <i data-lucide="credit-card" class="w-4 h-4 mr-2"></i>
                        Pay Now
                    </a>
                {/if}
            </div>
        </div>
    </div>

    {if $paymentsuccessful}
        <div class="alert alert-success mb-6">
            <i data-lucide="check-circle" class="w-4 h-4 mr-2 inline"></i>
            {$LANG.invoicepaymentsuccessconfirmation}
        </div>
    {/if}

    {if $paymentfailed}
        <div class="alert alert-danger mb-6">
            <i data-lucide="alert-circle" class="w-4 h-4 mr-2 inline"></i>
            {$LANG.invoicepaymentfailedconfirmation}
        </div>
    {/if}

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Invoice Details -->
        <div class="lg:col-span-2">
            <!-- Company & Client Information -->
            <div class="panel mb-6">
                <div class="panel-body">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                        <!-- Company Info -->
                        <div>
                            <h3 class="text-lg font-semibold text-white mb-4 flex items-center">
                                <i data-lucide="building" class="w-5 h-5 mr-2 text-blue-400"></i>
                                From
                            </h3>
                            <div class="text-gray-300">
                                <div class="font-semibold text-white text-xl mb-2">{$companyname}</div>
                                <div>{$companyaddress}</div>
                                {if $companycity}<div>{$companycity}, {$companystate} {$companypostcode}</div>{/if}
                                {if $companycountry}<div>{$companycountry}</div>{/if}
                                {if $companyphone}<div class="mt-2">Phone: {$companyphone}</div>{/if}
                                {if $companyemail}<div>Email: {$companyemail}</div>{/if}
                            </div>
                        </div>

                        <!-- Client Info -->
                        <div>
                            <h3 class="text-lg font-semibold text-white mb-4 flex items-center">
                                <i data-lucide="user" class="w-5 h-5 mr-2 text-purple-400"></i>
                                Bill To
                            </h3>
                            <div class="text-gray-300">
                                <div class="font-semibold text-white">{$clientsdetails.firstname} {$clientsdetails.lastname}</div>
                                {if $clientsdetails.companyname}<div>{$clientsdetails.companyname}</div>{/if}
                                <div>{$clientsdetails.address1}</div>
                                {if $clientsdetails.address2}<div>{$clientsdetails.address2}</div>{/if}
                                <div>{$clientsdetails.city}, {$clientsdetails.state} {$clientsdetails.postcode}</div>
                                <div>{$clientsdetails.country}</div>
                                <div class="mt-2">Email: {$clientsdetails.email}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Invoice Items -->
            <div class="panel mb-6">
                <div class="panel-heading">
                    <h3 class="flex items-center">
                        <i data-lucide="list" class="w-5 h-5 mr-2 text-green-400"></i>
                        Invoice Items
                    </h3>
                </div>
                <div class="panel-body p-0">
                    <div class="overflow-x-auto">
                        <table class="table w-full">
                            <thead>
                                <tr>
                                    <th class="text-left">Description</th>
                                    <th class="text-center">Quantity</th>
                                    <th class="text-right">Unit Price</th>
                                    <th class="text-right">Amount</th>
                                </tr>
                            </thead>
                            <tbody>
                                {foreach from=$invoiceitems item=item}
                                    <tr>
                                        <td>
                                            <div class="font-medium text-white">{$item.description}</div>
                                            {if $item.type eq "Domain"}
                                                <div class="text-sm text-gray-400">
                                                    <i data-lucide="globe" class="w-3 h-3 mr-1 inline"></i>
                                                    Domain Registration/Renewal
                                                </div>
                                            {elseif $item.type eq "Hosting"}
                                                <div class="text-sm text-gray-400">
                                                    <i data-lucide="server" class="w-3 h-3 mr-1 inline"></i>
                                                    Hosting Service
                                                </div>
                                            {/if}
                                        </td>
                                        <td class="text-center text-gray-300">
                                            {if $item.qty}
                                                {$item.qty}
                                            {else}
                                                1
                                            {/if}
                                        </td>
                                        <td class="text-right text-gray-300">
                                            {$item.amount}
                                        </td>
                                        <td class="text-right font-semibold text-white">
                                            {$item.amount}
                                        </td>
                                    </tr>
                                {/foreach}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Payment History -->
            {if $transactions}
                <div class="panel mb-6">
                    <div class="panel-heading">
                        <h3 class="flex items-center">
                            <i data-lucide="credit-card" class="w-5 h-5 mr-2 text-emerald-400"></i>
                            Payment History
                        </h3>
                    </div>
                    <div class="panel-body p-0">
                        <div class="overflow-x-auto">
                            <table class="table w-full">
                                <thead>
                                    <tr>
                                        <th class="text-left">Date</th>
                                        <th class="text-left">Gateway</th>
                                        <th class="text-left">Transaction ID</th>
                                        <th class="text-right">Amount</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {foreach from=$transactions item=transaction}
                                        <tr>
                                            <td class="text-gray-300">{$transaction.date}</td>
                                            <td class="text-gray-300">{$transaction.gateway}</td>
                                            <td class="text-gray-300 font-mono text-sm">{$transaction.transid}</td>
                                            <td class="text-right font-semibold text-green-400">{$transaction.amountin}</td>
                                        </tr>
                                    {/foreach}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            {/if}

            <!-- Invoice Actions -->
            <div class="flex flex-wrap gap-4">
                <a href="{$WEB_ROOT}/dl.php?type=i&id={$invoiceid}" class="btn btn-secondary" target="_blank">
                    <i data-lucide="download" class="w-4 h-4 mr-2"></i>
                    Download PDF
                </a>
                <a href="{$WEB_ROOT}/dl.php?type=i&id={$invoiceid}&print=true" class="btn btn-secondary" target="_blank">
                    <i data-lucide="printer" class="w-4 h-4 mr-2"></i>
                    Print Invoice
                </a>
                <a href="clientarea.php?action=invoices" class="btn btn-secondary">
                    <i data-lucide="arrow-left" class="w-4 h-4 mr-2"></i>
                    Back to Invoices
                </a>
            </div>
        </div>

        <!-- Invoice Summary -->
        <div class="lg:col-span-1">
            <div class="panel sticky top-24">
                <div class="panel-heading">
                    <h3 class="flex items-center">
                        <i data-lucide="calculator" class="w-5 h-5 mr-2 text-orange-400"></i>
                        Invoice Summary
                    </h3>
                </div>
                <div class="panel-body">
                    <!-- Invoice Totals -->
                    <div class="space-y-3 mb-6">
                        <div class="flex justify-between text-gray-300">
                            <span>Subtotal:</span>
                            <span>{$subtotal}</span>
                        </div>
                        
                        {if $taxrate}
                            <div class="flex justify-between text-gray-300">
                                <span>Tax ({$taxrate}%):</span>
                                <span>{$tax}</span>
                            </div>
                        {/if}
                        
                        {if $credit}
                            <div class="flex justify-between text-green-400">
                                <span>Credit Applied:</span>
                                <span>-{$credit}</span>
                            </div>
                        {/if}
                        
                        <div class="border-t border-slate-700 pt-3">
                            <div class="flex justify-between text-2xl font-bold text-white">
                                <span>Total:</span>
                                <span>{$total}</span>
                            </div>
                        </div>
                        
                        {if $balance}
                            <div class="flex justify-between text-xl font-semibold">
                                <span class="text-gray-300">Balance Due:</span>
                                <span class="{if $status eq 'Paid'}text-green-400{else}text-red-400{/if}">
                                    {$balance}
                                </span>
                            </div>
                        {/if}
                    </div>

                    <!-- Payment Options -->
                    {if $status eq "Unpaid" && $balance > 0}
                        <div class="space-y-3">
                            <h4 class="font-semibold text-white mb-3">Payment Options</h4>
                            
                            {if $paymentmethods}
                                {foreach from=$paymentmethods item=method}
                                    <a href="{$WEB_ROOT}/viewinvoice.php?id={$invoiceid}&paymentmethod={$method.sysname}" 
                                       class="btn btn-primary w-full">
                                        <i data-lucide="credit-card" class="w-4 h-4 mr-2"></i>
                                        Pay with {$method.name}
                                    </a>
                                {/foreach}
                            {else}
                                <a href="{$WEB_ROOT}/viewinvoice.php?id={$invoiceid}&paynow=true" 
                                   class="btn btn-primary w-full">
                                    <i data-lucide="credit-card" class="w-4 h-4 mr-2"></i>
                                    Pay Now
                                </a>
                            {/if}
                            
                            {if $allowcreditcard}
                                <div class="text-center mt-4">
                                    <div class="flex items-center justify-center space-x-2 text-sm text-gray-400">
                                        <i data-lucide="shield-check" class="w-4 h-4 text-green-400"></i>
                                        <span>Secure SSL Payment</span>
                                    </div>
                                </div>
                            {/if}
                        </div>
                    {elseif $status eq "Paid"}
                        <div class="text-center p-4 bg-green-500/10 border border-green-500/20 rounded-lg">
                            <i data-lucide="check-circle" class="w-8 h-8 text-green-400 mx-auto mb-2"></i>
                            <h4 class="font-semibold text-green-400 mb-1">Payment Complete</h4>
                            <p class="text-sm text-gray-400">This invoice has been paid in full</p>
                        </div>
                    {/if}

                    <!-- Invoice Information -->
                    <div class="mt-6 pt-6 border-t border-slate-700">
                        <div class="space-y-2 text-sm">
                            <div class="flex justify-between">
                                <span class="text-gray-400">Invoice #:</span>
                                <span class="text-white font-mono">{$invoicenum}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-400">Date Created:</span>
                                <span class="text-white">{$date}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-400">Due Date:</span>
                                <span class="text-white">{$datedue}</span>
                            </div>
                            {if $datepaid}
                                <div class="flex justify-between">
                                    <span class="text-gray-400">Date Paid:</span>
                                    <span class="text-green-400">{$datepaid}</span>
                                </div>
                            {/if}
                        </div>
                    </div>

                    <!-- Support -->
                    <div class="mt-6 pt-6 border-t border-slate-700">
                        <h4 class="font-semibold text-white mb-3">Need Help?</h4>
                        <div class="space-y-2">
                            <a href="submitticket.php" class="btn btn-secondary w-full text-sm">
                                <i data-lucide="life-buoy" class="w-3 h-3 mr-2"></i>
                                Contact Support
                            </a>
                            <a href="knowledgebase.php" class="btn btn-secondary w-full text-sm">
                                <i data-lucide="book-open" class="w-3 h-3 mr-2"></i>
                                Knowledge Base
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{include file="$template/footer.tpl"}
