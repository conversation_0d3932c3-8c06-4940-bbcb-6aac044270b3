{include file="$template/header.tpl"}

<div class="container mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Support Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-white mb-2">
                    <i data-lucide="life-buoy" class="w-8 h-8 mr-3 inline text-orange-400"></i>
                    Support Tickets
                </h1>
                <p class="text-gray-300">Manage your support requests and get help from our experts</p>
            </div>
            <div>
                <a href="submitticket.php" class="btn btn-primary">
                    <i data-lucide="plus" class="w-4 h-4 mr-2"></i>
                    Open New Ticket
                </a>
            </div>
        </div>
    </div>

    {if $infomessage}
        <div class="alert alert-info mb-6">
            <i data-lucide="info" class="w-4 h-4 mr-2 inline"></i>
            {$infomessage}
        </div>
    {/if}

    {if $tickets}
        <!-- Tickets Filter -->
        <div class="panel mb-6">
            <div class="panel-body">
                <div class="flex flex-wrap items-center gap-4">
                    <div class="flex items-center">
                        <label class="text-sm text-gray-300 mr-2">Filter by Status:</label>
                        <select class="form-control w-auto" onchange="filterTickets(this.value)">
                            <option value="">All Tickets</option>
                            <option value="Open">Open</option>
                            <option value="Answered">Answered</option>
                            <option value="Customer-Reply">Customer Reply</option>
                            <option value="Closed">Closed</option>
                        </select>
                    </div>
                    <div class="flex items-center">
                        <label class="text-sm text-gray-300 mr-2">Search:</label>
                        <input type="text" class="form-control w-auto" placeholder="Search tickets..." onkeyup="searchTickets(this.value)">
                    </div>
                </div>
            </div>
        </div>

        <!-- Tickets List -->
        <div class="panel">
            <div class="panel-heading">
                <h3 class="flex items-center">
                    <i data-lucide="ticket" class="w-5 h-5 mr-2 text-blue-400"></i>
                    Your Support Tickets ({$tickets|count})
                </h3>
            </div>
            <div class="panel-body p-0">
                <div class="overflow-x-auto">
                    <table class="table w-full">
                        <thead>
                            <tr>
                                <th class="text-left">
                                    <i data-lucide="hash" class="w-4 h-4 mr-1 inline"></i>
                                    Ticket #
                                </th>
                                <th class="text-left">
                                    <i data-lucide="file-text" class="w-4 h-4 mr-1 inline"></i>
                                    Subject
                                </th>
                                <th class="text-left">
                                    <i data-lucide="folder" class="w-4 h-4 mr-1 inline"></i>
                                    Department
                                </th>
                                <th class="text-left">
                                    <i data-lucide="flag" class="w-4 h-4 mr-1 inline"></i>
                                    Priority
                                </th>
                                <th class="text-left">
                                    <i data-lucide="activity" class="w-4 h-4 mr-1 inline"></i>
                                    Status
                                </th>
                                <th class="text-left">
                                    <i data-lucide="clock" class="w-4 h-4 mr-1 inline"></i>
                                    Last Updated
                                </th>
                                <th class="text-center">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {foreach from=$tickets item=ticket}
                                <tr class="ticket-row" data-status="{$ticket.status}" data-subject="{$ticket.subject|lower}">
                                    <td>
                                        <div class="flex items-center">
                                            <div class="w-8 h-8 bg-gradient-to-r from-blue-500/20 to-purple-500/20 rounded-lg flex items-center justify-center mr-3">
                                                <i data-lucide="ticket" class="w-4 h-4 text-blue-400"></i>
                                            </div>
                                            <span class="font-semibold text-white">#{$ticket.tid}</span>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="max-w-xs">
                                            <h4 class="font-medium text-white truncate">{$ticket.subject}</h4>
                                            <p class="text-sm text-gray-400 truncate">{$ticket.message|truncate:50}</p>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="text-gray-300">{$ticket.department}</span>
                                    </td>
                                    <td>
                                        <span class="badge badge-{$ticket.urgency|lower}">
                                            {$ticket.urgency}
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge badge-{$ticket.status|lower|replace:' ':'-'}">
                                            {$ticket.status}
                                        </span>
                                    </td>
                                    <td>
                                        <div class="text-sm">
                                            <div class="text-gray-300">{$ticket.lastreply}</div>
                                            <div class="text-gray-500">{$ticket.date}</div>
                                        </div>
                                    </td>
                                    <td class="text-center">
                                        <div class="flex items-center justify-center space-x-2">
                                            <a href="supporttickets.php?action=view&id={$ticket.tid}" 
                                               class="btn btn-sm btn-secondary" 
                                               title="View Ticket">
                                                <i data-lucide="eye" class="w-3 h-3"></i>
                                            </a>
                                            {if $ticket.status neq "Closed"}
                                                <a href="supporttickets.php?action=view&id={$ticket.tid}#reply" 
                                                   class="btn btn-sm btn-primary" 
                                                   title="Reply">
                                                    <i data-lucide="reply" class="w-3 h-3"></i>
                                                </a>
                                            {/if}
                                        </div>
                                    </td>
                                </tr>
                            {/foreach}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Pagination -->
        {if $pagination}
            <div class="mt-6 flex justify-center">
                {$pagination}
            </div>
        {/if}

    {else}
        <!-- Empty State -->
        <div class="panel">
            <div class="panel-body text-center py-16">
                <div class="w-24 h-24 bg-slate-800/50 rounded-full flex items-center justify-center mx-auto mb-6">
                    <i data-lucide="life-buoy" class="w-12 h-12 text-gray-400"></i>
                </div>
                <h3 class="text-2xl font-semibold text-white mb-4">No Support Tickets</h3>
                <p class="text-gray-400 mb-8 max-w-md mx-auto">
                    You haven't submitted any support tickets yet. Our expert team is here to help whenever you need assistance.
                </p>
                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                    <a href="submitticket.php" class="btn btn-primary">
                        <i data-lucide="plus" class="w-4 h-4 mr-2"></i>
                        Open Support Ticket
                    </a>
                    <a href="knowledgebase.php" class="btn btn-secondary">
                        <i data-lucide="book-open" class="w-4 h-4 mr-2"></i>
                        Browse Knowledge Base
                    </a>
                </div>
            </div>
        </div>
    {/if}

    <!-- Support Information -->
    <div class="mt-8 grid grid-cols-1 md:grid-cols-3 gap-6">
        <div class="panel">
            <div class="panel-body text-center">
                <div class="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i data-lucide="clock" class="w-6 h-6 text-white"></i>
                </div>
                <h4 class="font-semibold text-white mb-2">24/7 Support</h4>
                <p class="text-gray-400 text-sm">
                    Our expert support team is available around the clock to help you with any issues.
                </p>
            </div>
        </div>

        <div class="panel">
            <div class="panel-body text-center">
                <div class="w-12 h-12 bg-gradient-to-r from-emerald-500 to-teal-500 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i data-lucide="zap" class="w-6 h-6 text-white"></i>
                </div>
                <h4 class="font-semibold text-white mb-2">Fast Response</h4>
                <p class="text-gray-400 text-sm">
                    Average response time under 30 minutes for urgent issues and 2 hours for general inquiries.
                </p>
            </div>
        </div>

        <div class="panel">
            <div class="panel-body text-center">
                <div class="w-12 h-12 bg-gradient-to-r from-orange-500 to-red-500 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i data-lucide="users" class="w-6 h-6 text-white"></i>
                </div>
                <h4 class="font-semibold text-white mb-2">Expert Team</h4>
                <p class="text-gray-400 text-sm">
                    Our certified engineers have years of experience in hosting and server management.
                </p>
            </div>
        </div>
    </div>
</div>

<script>
function filterTickets(status) {
    const rows = document.querySelectorAll('.ticket-row');
    rows.forEach(row => {
        if (status === '' || row.dataset.status === status) {
            row.style.display = '';
        } else {
            row.style.display = 'none';
        }
    });
}

function searchTickets(query) {
    const rows = document.querySelectorAll('.ticket-row');
    const searchTerm = query.toLowerCase();
    
    rows.forEach(row => {
        const subject = row.dataset.subject;
        const ticketId = row.querySelector('td:first-child span').textContent.toLowerCase();
        
        if (subject.includes(searchTerm) || ticketId.includes(searchTerm)) {
            row.style.display = '';
        } else {
            row.style.display = 'none';
        }
    });
}
</script>

{include file="$template/footer.tpl"}
