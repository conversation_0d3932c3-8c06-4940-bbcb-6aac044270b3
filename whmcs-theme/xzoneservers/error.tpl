{include file="$template/header.tpl"}

<div class="container mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <div class="max-w-2xl mx-auto">
        <!-- Error Display -->
        <div class="panel">
            <div class="panel-body text-center py-16">
                <!-- Error Icon -->
                <div class="w-24 h-24 bg-gradient-to-r from-red-500/20 to-orange-500/20 rounded-full flex items-center justify-center mx-auto mb-6">
                    {if $errortype eq '404'}
                        <i data-lucide="search-x" class="w-12 h-12 text-red-400"></i>
                    {elseif $errortype eq '403'}
                        <i data-lucide="shield-x" class="w-12 h-12 text-orange-400"></i>
                    {elseif $errortype eq '500'}
                        <i data-lucide="server-crash" class="w-12 h-12 text-red-400"></i>
                    {else}
                        <i data-lucide="alert-triangle" class="w-12 h-12 text-yellow-400"></i>
                    {/if}
                </div>

                <!-- Error Title -->
                <h1 class="text-4xl font-bold text-white mb-4">
                    {if $errortype eq '404'}
                        Page Not Found
                    {elseif $errortype eq '403'}
                        Access Forbidden
                    {elseif $errortype eq '500'}
                        Server Error
                    {else}
                        Oops! Something went wrong
                    {/if}
                </h1>

                <!-- Error Code -->
                {if $errortype}
                    <div class="text-6xl font-bold text-transparent bg-gradient-to-r from-red-400 to-orange-400 bg-clip-text mb-6">
                        {$errortype}
                    </div>
                {/if}

                <!-- Error Message -->
                <div class="text-gray-300 mb-8 max-w-md mx-auto">
                    {if $errormessage}
                        <p class="mb-4">{$errormessage}</p>
                    {else}
                        {if $errortype eq '404'}
                            <p class="mb-4">The page you're looking for doesn't exist or has been moved.</p>
                        {elseif $errortype eq '403'}
                            <p class="mb-4">You don't have permission to access this resource.</p>
                        {elseif $errortype eq '500'}
                            <p class="mb-4">We're experiencing technical difficulties. Please try again later.</p>
                        {else}
                            <p class="mb-4">We encountered an unexpected error. Our team has been notified.</p>
                        {/if}
                    {/if}
                    
                    {if $errortype eq '404'}
                        <p class="text-sm text-gray-400">
                            Double-check the URL or use the navigation menu to find what you're looking for.
                        </p>
                    {elseif $errortype eq '403'}
                        <p class="text-sm text-gray-400">
                            Please log in or contact support if you believe this is an error.
                        </p>
                    {elseif $errortype eq '500'}
                        <p class="text-sm text-gray-400">
                            If the problem persists, please contact our support team.
                        </p>
                    {/if}
                </div>

                <!-- Action Buttons -->
                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                    <button onclick="history.back()" class="btn btn-secondary">
                        <i data-lucide="arrow-left" class="w-4 h-4 mr-2"></i>
                        Go Back
                    </button>
                    <a href="{$WEB_ROOT}/" class="btn btn-primary">
                        <i data-lucide="home" class="w-4 h-4 mr-2"></i>
                        Go Home
                    </a>
                    {if $loggedin}
                        <a href="clientarea.php" class="btn btn-secondary">
                            <i data-lucide="user" class="w-4 h-4 mr-2"></i>
                            Client Area
                        </a>
                    {else}
                        <a href="login.php" class="btn btn-secondary">
                            <i data-lucide="log-in" class="w-4 h-4 mr-2"></i>
                            Login
                        </a>
                    {/if}
                </div>
            </div>
        </div>

        <!-- Help Section -->
        <div class="mt-8">
            <div class="bg-gradient-to-r from-blue-500/10 to-purple-500/10 border border-blue-500/20 rounded-xl p-6">
                <h3 class="text-xl font-semibold text-white mb-4 flex items-center">
                    <i data-lucide="help-circle" class="w-5 h-5 mr-2 text-blue-400"></i>
                    Need Help?
                </h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <h4 class="font-semibold text-white mb-3">Quick Links</h4>
                        <div class="space-y-2">
                            <a href="{$WEB_ROOT}/knowledgebase.php" class="flex items-center text-gray-300 hover:text-blue-400 transition-colors">
                                <i data-lucide="book-open" class="w-4 h-4 mr-2"></i>
                                Knowledge Base
                            </a>
                            <a href="{$WEB_ROOT}/submitticket.php" class="flex items-center text-gray-300 hover:text-blue-400 transition-colors">
                                <i data-lucide="life-buoy" class="w-4 h-4 mr-2"></i>
                                Contact Support
                            </a>
                            <a href="{$WEB_ROOT}/serverstatus.php" class="flex items-center text-gray-300 hover:text-blue-400 transition-colors">
                                <i data-lucide="activity" class="w-4 h-4 mr-2"></i>
                                Server Status
                            </a>
                            <a href="{$WEB_ROOT}/announcements.php" class="flex items-center text-gray-300 hover:text-blue-400 transition-colors">
                                <i data-lucide="megaphone" class="w-4 h-4 mr-2"></i>
                                Announcements
                            </a>
                        </div>
                    </div>
                    
                    <div>
                        <h4 class="font-semibold text-white mb-3">Popular Services</h4>
                        <div class="space-y-2">
                            <a href="{$WEB_ROOT}/cart.php?a=add&pid=1" class="flex items-center text-gray-300 hover:text-blue-400 transition-colors">
                                <i data-lucide="server" class="w-4 h-4 mr-2"></i>
                                Web Hosting
                            </a>
                            <a href="{$WEB_ROOT}/cart.php?a=add&pid=2" class="flex items-center text-gray-300 hover:text-blue-400 transition-colors">
                                <i data-lucide="hard-drive" class="w-4 h-4 mr-2"></i>
                                VPS Hosting
                            </a>
                            <a href="{$WEB_ROOT}/cart.php?a=add&domain=register" class="flex items-center text-gray-300 hover:text-blue-400 transition-colors">
                                <i data-lucide="globe" class="w-4 h-4 mr-2"></i>
                                Domain Registration
                            </a>
                            <a href="{$WEB_ROOT}/cart.php?a=add&pid=ssl" class="flex items-center text-gray-300 hover:text-blue-400 transition-colors">
                                <i data-lucide="shield-check" class="w-4 h-4 mr-2"></i>
                                SSL Certificates
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Contact Information -->
        <div class="mt-8">
            <div class="panel">
                <div class="panel-heading">
                    <h3 class="flex items-center">
                        <i data-lucide="phone" class="w-5 h-5 mr-2 text-emerald-400"></i>
                        Contact Information
                    </h3>
                </div>
                <div class="panel-body">
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div class="text-center">
                            <div class="w-12 h-12 bg-blue-500/20 rounded-lg flex items-center justify-center mx-auto mb-3">
                                <i data-lucide="message-circle" class="w-6 h-6 text-blue-400"></i>
                            </div>
                            <h4 class="font-semibold text-white mb-2">Live Chat</h4>
                            <p class="text-gray-400 text-sm mb-3">Available 24/7 for immediate assistance</p>
                            <button class="btn btn-sm btn-primary" onclick="openLiveChat()">
                                Start Chat
                            </button>
                        </div>
                        
                        <div class="text-center">
                            <div class="w-12 h-12 bg-purple-500/20 rounded-lg flex items-center justify-center mx-auto mb-3">
                                <i data-lucide="mail" class="w-6 h-6 text-purple-400"></i>
                            </div>
                            <h4 class="font-semibold text-white mb-2">Email Support</h4>
                            <p class="text-gray-400 text-sm mb-3">Get detailed help via email</p>
                            <a href="mailto:<EMAIL>" class="btn btn-sm btn-secondary">
                                Send Email
                            </a>
                        </div>
                        
                        <div class="text-center">
                            <div class="w-12 h-12 bg-green-500/20 rounded-lg flex items-center justify-center mx-auto mb-3">
                                <i data-lucide="phone" class="w-6 h-6 text-green-400"></i>
                            </div>
                            <h4 class="font-semibold text-white mb-2">Phone Support</h4>
                            <p class="text-gray-400 text-sm mb-3">For urgent technical issues</p>
                            <a href="tel:+1234567890" class="btn btn-sm btn-secondary">
                                Call Now
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Auto-refresh for server errors
{if $errortype eq '500'}
    let refreshCount = 0;
    const maxRefreshes = 3;
    
    function autoRefresh() {
        if (refreshCount < maxRefreshes) {
            refreshCount++;
            setTimeout(() => {
                window.location.reload();
            }, 30000); // Refresh after 30 seconds
        }
    }
    
    // Start auto-refresh for server errors
    autoRefresh();
{/if}

// Live chat function (placeholder)
function openLiveChat() {
    // Implement your live chat integration here
    // For example: Intercom, Zendesk Chat, etc.
    alert('Live chat feature would be integrated here');
}

// Track error for analytics
{if $errortype}
    // Send error tracking data to analytics
    if (typeof gtag !== 'undefined') {
        gtag('event', 'error_page_view', {
            'error_type': '{$errortype}',
            'page_location': window.location.href,
            'page_title': document.title
        });
    }
{/if}

// Keyboard shortcuts
document.addEventListener('keydown', function(event) {
    // Press 'H' to go home
    if (event.key === 'h' || event.key === 'H') {
        if (!event.ctrlKey && !event.metaKey && !event.altKey) {
            window.location.href = '{$WEB_ROOT}/';
        }
    }
    
    // Press 'B' to go back
    if (event.key === 'b' || event.key === 'B') {
        if (!event.ctrlKey && !event.metaKey && !event.altKey) {
            history.back();
        }
    }
    
    // Press 'S' to contact support
    if (event.key === 's' || event.key === 'S') {
        if (!event.ctrlKey && !event.metaKey && !event.altKey) {
            window.location.href = '{$WEB_ROOT}/submitticket.php';
        }
    }
});

// Show keyboard shortcuts hint
document.addEventListener('DOMContentLoaded', function() {
    const hint = document.createElement('div');
    hint.className = 'fixed bottom-4 right-4 bg-slate-800/90 backdrop-blur-sm border border-slate-700/50 rounded-lg p-3 text-xs text-gray-400 max-w-xs';
    hint.innerHTML = `
        <div class="font-semibold text-white mb-1">Keyboard Shortcuts:</div>
        <div>Press <kbd class="bg-slate-700 px-1 rounded">H</kbd> for Home</div>
        <div>Press <kbd class="bg-slate-700 px-1 rounded">B</kbd> to go Back</div>
        <div>Press <kbd class="bg-slate-700 px-1 rounded">S</kbd> for Support</div>
    `;
    
    document.body.appendChild(hint);
    
    // Hide hint after 10 seconds
    setTimeout(() => {
        hint.style.opacity = '0';
        hint.style.transition = 'opacity 0.5s';
        setTimeout(() => hint.remove(), 500);
    }, 10000);
});
</script>

{include file="$template/footer.tpl"}
