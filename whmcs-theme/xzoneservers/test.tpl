<!DOCTYPE html>
<html>
<head>
    <title>X-ZoneServers Theme Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: #020617;
            color: white;
            padding: 20px;
        }
        .test-panel {
            background: #0f172a;
            border: 1px solid #334155;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <h1>X-ZoneServers WHMCS Theme Test</h1>
    
    <div class="test-panel">
        <h2>Template Variables Test</h2>
        <p><strong>Template:</strong> {$template}</p>
        <p><strong>WEB_ROOT:</strong> {$WEB_ROOT}</p>
        <p><strong>Company Name:</strong> {$companyname}</p>
        <p><strong>Page Title:</strong> {$pagetitle}</p>
        <p><strong>Template File:</strong> {$templatefile}</p>
    </div>
    
    <div class="test-panel">
        <h2>CSS Test</h2>
        <p>If you can see this styled correctly, the basic template is working.</p>
        <button style="background: #0ea5e9; color: white; padding: 10px 20px; border: none; border-radius: 4px;">Test Button</button>
    </div>
    
    <div class="test-panel">
        <h2>File Paths</h2>
        <p><strong>CSS Path:</strong> {$WEB_ROOT}/templates/{$template}/css/styles.css</p>
        <p><strong>JS Path:</strong> {$WEB_ROOT}/templates/{$template}/js/theme.js</p>
    </div>
    
    <script>
        console.log('Test template loaded successfully');
        console.log('Template variables:', {
            template: '{$template}',
            webRoot: '{$WEB_ROOT}',
            companyName: '{$companyname}'
        });
    </script>
</body>
</html>
