{include file="$template/header.tpl"}

<div class="container mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Page Header -->
    <div class="mb-8 text-center">
        <h1 class="text-4xl font-bold text-white mb-4">Contact Us</h1>
        <p class="text-xl text-gray-300 max-w-2xl mx-auto">
            Get in touch with our expert support team. We're here to help you 24/7.
        </p>
    </div>

    {if $sent}
        <div class="alert alert-success mb-8">
            <i data-lucide="check-circle" class="w-4 h-4 mr-2 inline"></i>
            Your message has been sent successfully! We'll get back to you within 24 hours.
        </div>
    {/if}

    {if $errormessage}
        <div class="alert alert-danger mb-8">
            <i data-lucide="alert-circle" class="w-4 h-4 mr-2 inline"></i>
            {$errormessage}
        </div>
    {/if}

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Contact Form -->
        <div class="lg:col-span-2">
            <div class="panel">
                <div class="panel-heading">
                    <h3 class="flex items-center">
                        <i data-lucide="send" class="w-5 h-5 mr-2 text-blue-400"></i>
                        Send us a Message
                    </h3>
                </div>
                <div class="panel-body">
                    <form method="post" action="{$smarty.server.PHP_SELF}" class="space-y-6">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div class="form-group">
                                <label for="name" class="form-label required">
                                    <i data-lucide="user" class="w-4 h-4 mr-1 inline"></i>
                                    Full Name
                                </label>
                                <input type="text" 
                                       name="name" 
                                       id="name" 
                                       value="{$name}" 
                                       class="form-control" 
                                       placeholder="Your full name"
                                       required>
                            </div>

                            <div class="form-group">
                                <label for="email" class="form-label required">
                                    <i data-lucide="mail" class="w-4 h-4 mr-1 inline"></i>
                                    Email Address
                                </label>
                                <input type="email" 
                                       name="email" 
                                       id="email" 
                                       value="{$email}" 
                                       class="form-control" 
                                       placeholder="<EMAIL>"
                                       required>
                            </div>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div class="form-group">
                                <label for="subject" class="form-label required">
                                    <i data-lucide="tag" class="w-4 h-4 mr-1 inline"></i>
                                    Subject
                                </label>
                                <select name="subject" id="subject" class="form-control" required>
                                    <option value="">Select a subject</option>
                                    <option value="General Inquiry">General Inquiry</option>
                                    <option value="Technical Support">Technical Support</option>
                                    <option value="Billing Question">Billing Question</option>
                                    <option value="Sales Question">Sales Question</option>
                                    <option value="Partnership">Partnership Opportunity</option>
                                    <option value="Feedback">Feedback</option>
                                    <option value="Other">Other</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label for="phone" class="form-label">
                                    <i data-lucide="phone" class="w-4 h-4 mr-1 inline"></i>
                                    Phone Number (Optional)
                                </label>
                                <input type="tel" 
                                       name="phone" 
                                       id="phone" 
                                       value="{$phone}" 
                                       class="form-control" 
                                       placeholder="+****************">
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="message" class="form-label required">
                                <i data-lucide="message-square" class="w-4 h-4 mr-1 inline"></i>
                                Message
                            </label>
                            <textarea name="message" 
                                      id="message" 
                                      rows="6" 
                                      class="form-control" 
                                      placeholder="Please provide as much detail as possible about your inquiry..."
                                      required>{$message}</textarea>
                            <div class="text-sm text-gray-400 mt-2">
                                <i data-lucide="info" class="w-3 h-3 mr-1 inline"></i>
                                Minimum 20 characters required
                            </div>
                        </div>

                        {if $captcha}
                            <div class="form-group">
                                <label class="form-label">
                                    <i data-lucide="shield-check" class="w-4 h-4 mr-1 inline"></i>
                                    Security Verification
                                </label>
                                <div class="captcha-container">
                                    {$captcha}
                                </div>
                            </div>
                        {/if}

                        <div class="flex items-center">
                            <input type="checkbox" 
                                   id="newsletter" 
                                   name="newsletter" 
                                   class="form-checkbox mr-3">
                            <label for="newsletter" class="text-gray-300 text-sm">
                                I would like to receive updates about new services and promotions
                            </label>
                        </div>

                        <div class="flex flex-col sm:flex-row gap-4">
                            <button type="submit" class="btn btn-primary flex-1">
                                <i data-lucide="send" class="w-4 h-4 mr-2"></i>
                                Send Message
                            </button>
                            <button type="reset" class="btn btn-secondary flex-1">
                                <i data-lucide="refresh-cw" class="w-4 h-4 mr-2"></i>
                                Clear Form
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Contact Information -->
        <div class="lg:col-span-1">
            <!-- Contact Methods -->
            <div class="panel mb-6">
                <div class="panel-heading">
                    <h3 class="flex items-center">
                        <i data-lucide="phone" class="w-5 h-5 mr-2 text-emerald-400"></i>
                        Get in Touch
                    </h3>
                </div>
                <div class="panel-body">
                    <div class="space-y-6">
                        <div class="flex items-start">
                            <div class="w-10 h-10 bg-blue-500/20 rounded-lg flex items-center justify-center mr-4">
                                <i data-lucide="message-circle" class="w-5 h-5 text-blue-400"></i>
                            </div>
                            <div>
                                <h4 class="font-semibold text-white mb-1">Live Chat</h4>
                                <p class="text-gray-400 text-sm mb-2">Available 24/7</p>
                                <button class="text-blue-400 hover:text-blue-300 text-sm" onclick="openLiveChat()">
                                    Start Chat →
                                </button>
                            </div>
                        </div>

                        <div class="flex items-start">
                            <div class="w-10 h-10 bg-purple-500/20 rounded-lg flex items-center justify-center mr-4">
                                <i data-lucide="mail" class="w-5 h-5 text-purple-400"></i>
                            </div>
                            <div>
                                <h4 class="font-semibold text-white mb-1">Email Support</h4>
                                <p class="text-gray-400 text-sm mb-2">Response within 2 hours</p>
                                <a href="mailto:<EMAIL>" class="text-purple-400 hover:text-purple-300 text-sm">
                                    <EMAIL>
                                </a>
                            </div>
                        </div>

                        <div class="flex items-start">
                            <div class="w-10 h-10 bg-green-500/20 rounded-lg flex items-center justify-center mr-4">
                                <i data-lucide="phone" class="w-5 h-5 text-green-400"></i>
                            </div>
                            <div>
                                <h4 class="font-semibold text-white mb-1">Phone Support</h4>
                                <p class="text-gray-400 text-sm mb-2">Emergency line</p>
                                <a href="tel:+1234567890" class="text-green-400 hover:text-green-300 text-sm">
                                    +****************
                                </a>
                            </div>
                        </div>

                        <div class="flex items-start">
                            <div class="w-10 h-10 bg-orange-500/20 rounded-lg flex items-center justify-center mr-4">
                                <i data-lucide="life-buoy" class="w-5 h-5 text-orange-400"></i>
                            </div>
                            <div>
                                <h4 class="font-semibold text-white mb-1">Support Tickets</h4>
                                <p class="text-gray-400 text-sm mb-2">Track your requests</p>
                                <a href="submitticket.php" class="text-orange-400 hover:text-orange-300 text-sm">
                                    Submit Ticket →
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Business Hours -->
            <div class="panel mb-6">
                <div class="panel-heading">
                    <h3 class="flex items-center">
                        <i data-lucide="clock" class="w-5 h-5 mr-2 text-yellow-400"></i>
                        Business Hours
                    </h3>
                </div>
                <div class="panel-body">
                    <div class="space-y-3 text-sm">
                        <div class="flex justify-between">
                            <span class="text-gray-300">Monday - Friday:</span>
                            <span class="text-white">9:00 AM - 6:00 PM EST</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-300">Saturday:</span>
                            <span class="text-white">10:00 AM - 4:00 PM EST</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-300">Sunday:</span>
                            <span class="text-white">Emergency Only</span>
                        </div>
                        <div class="pt-3 border-t border-slate-700/50">
                            <div class="flex items-center">
                                <div class="w-2 h-2 bg-green-400 rounded-full mr-2"></div>
                                <span class="text-green-400 text-sm">Live Chat: 24/7 Available</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Office Location -->
            <div class="panel">
                <div class="panel-heading">
                    <h3 class="flex items-center">
                        <i data-lucide="map-pin" class="w-5 h-5 mr-2 text-red-400"></i>
                        Office Location
                    </h3>
                </div>
                <div class="panel-body">
                    <div class="text-gray-300 text-sm space-y-2">
                        <div class="font-semibold text-white">X-ZoneServers Headquarters</div>
                        <div>123 Tech Street</div>
                        <div>Digital City, DC 12345</div>
                        <div>United States</div>
                        
                        <div class="pt-4">
                            <a href="https://maps.google.com" 
                               target="_blank" 
                               class="btn btn-sm btn-secondary w-full">
                                <i data-lucide="map" class="w-3 h-3 mr-2"></i>
                                View on Map
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- FAQ Section -->
    <div class="mt-12">
        <div class="text-center mb-8">
            <h2 class="text-3xl font-bold text-white mb-4">Frequently Asked Questions</h2>
            <p class="text-gray-300">Quick answers to common questions</p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="panel">
                <div class="panel-body">
                    <h4 class="font-semibold text-white mb-3 flex items-center">
                        <i data-lucide="help-circle" class="w-4 h-4 mr-2 text-blue-400"></i>
                        How quickly do you respond to support requests?
                    </h4>
                    <p class="text-gray-400 text-sm">
                        We respond to all support requests within 2 hours during business hours, 
                        and within 4 hours outside business hours. Critical issues are handled immediately.
                    </p>
                </div>
            </div>

            <div class="panel">
                <div class="panel-body">
                    <h4 class="font-semibold text-white mb-3 flex items-center">
                        <i data-lucide="help-circle" class="w-4 h-4 mr-2 text-blue-400"></i>
                        Do you offer phone support?
                    </h4>
                    <p class="text-gray-400 text-sm">
                        Yes, we offer phone support for urgent technical issues and existing customers. 
                        Our phone lines are available during business hours.
                    </p>
                </div>
            </div>

            <div class="panel">
                <div class="panel-body">
                    <h4 class="font-semibold text-white mb-3 flex items-center">
                        <i data-lucide="help-circle" class="w-4 h-4 mr-2 text-blue-400"></i>
                        Can I schedule a consultation?
                    </h4>
                    <p class="text-gray-400 text-sm">
                        Absolutely! We offer free consultations for businesses looking for custom hosting solutions. 
                        Contact us to schedule a call with our technical team.
                    </p>
                </div>
            </div>

            <div class="panel">
                <div class="panel-body">
                    <h4 class="font-semibold text-white mb-3 flex items-center">
                        <i data-lucide="help-circle" class="w-4 h-4 mr-2 text-blue-400"></i>
                        Do you provide migration assistance?
                    </h4>
                    <p class="text-gray-400 text-sm">
                        Yes, we provide free migration assistance for all new customers. 
                        Our team will help transfer your websites and data with zero downtime.
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Form validation
document.addEventListener('DOMContentLoaded', function() {
    const form = document.querySelector('form');
    const messageField = document.getElementById('message');
    
    if (form && messageField) {
        form.addEventListener('submit', function(e) {
            const message = messageField.value.trim();
            
            if (message.length < 20) {
                e.preventDefault();
                alert('Please provide a more detailed message (minimum 20 characters).');
                messageField.focus();
                return false;
            }
        });
        
        // Character counter for message field
        const counter = document.createElement('div');
        counter.className = 'text-xs text-gray-500 mt-1';
        messageField.parentNode.appendChild(counter);
        
        function updateCounter() {
            const length = messageField.value.length;
            counter.textContent = `${length}/20 characters minimum`;
            counter.className = length >= 20 ? 'text-xs text-green-400 mt-1' : 'text-xs text-gray-500 mt-1';
        }
        
        messageField.addEventListener('input', updateCounter);
        updateCounter();
    }
});

// Live chat placeholder
function openLiveChat() {
    // Implement your live chat integration here
    alert('Live chat feature would be integrated here');
}
</script>

{include file="$template/footer.tpl"}
