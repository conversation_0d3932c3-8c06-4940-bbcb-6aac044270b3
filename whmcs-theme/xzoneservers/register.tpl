{include file="$template/header.tpl"}

<div class="container mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <div class="max-w-2xl mx-auto">
        <!-- Registration Header -->
        <div class="text-center mb-8">
            <h1 class="text-3xl font-bold text-white mb-4">
                <i data-lucide="user-plus" class="w-8 h-8 mr-3 inline text-blue-400"></i>
                Create Your Account
            </h1>
            <p class="text-gray-300 text-lg">
                Join thousands of satisfied customers and get started with enterprise-grade hosting
            </p>
        </div>

        <!-- Registration Form -->
        <div class="panel">
            <div class="panel-body">
                {if $errormessage}
                    <div class="alert alert-danger mb-6">
                        <i data-lucide="alert-circle" class="w-4 h-4 mr-2 inline"></i>
                        {$errormessage}
                    </div>
                {/if}

                <form method="post" action="{$smarty.server.PHP_SELF}" class="registration-form">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Personal Information -->
                        <div class="md:col-span-2">
                            <h3 class="text-xl font-semibold text-white mb-4 flex items-center">
                                <i data-lucide="user" class="w-5 h-5 mr-2 text-blue-400"></i>
                                Personal Information
                            </h3>
                        </div>

                        <div class="form-group">
                            <label for="inputFirstName" class="form-label required">
                                {$LANG.clientareafirstname}
                            </label>
                            <input type="text" 
                                   name="firstname" 
                                   id="inputFirstName" 
                                   value="{$clientsdetails.firstname}" 
                                   class="form-control" 
                                   required>
                        </div>

                        <div class="form-group">
                            <label for="inputLastName" class="form-label required">
                                {$LANG.clientarealastname}
                            </label>
                            <input type="text" 
                                   name="lastname" 
                                   id="inputLastName" 
                                   value="{$clientsdetails.lastname}" 
                                   class="form-control" 
                                   required>
                        </div>

                        <div class="form-group">
                            <label for="inputCompanyName" class="form-label">
                                {$LANG.clientareacompanyname}
                            </label>
                            <input type="text" 
                                   name="companyname" 
                                   id="inputCompanyName" 
                                   value="{$clientsdetails.companyname}" 
                                   class="form-control">
                        </div>

                        <div class="form-group">
                            <label for="inputEmail" class="form-label required">
                                {$LANG.clientareaemail}
                            </label>
                            <input type="email" 
                                   name="email" 
                                   id="inputEmail" 
                                   value="{$clientsdetails.email}" 
                                   class="form-control" 
                                   required>
                        </div>

                        <!-- Contact Information -->
                        <div class="md:col-span-2 mt-6">
                            <h3 class="text-xl font-semibold text-white mb-4 flex items-center">
                                <i data-lucide="map-pin" class="w-5 h-5 mr-2 text-purple-400"></i>
                                Contact Information
                            </h3>
                        </div>

                        <div class="form-group">
                            <label for="inputAddress1" class="form-label required">
                                {$LANG.clientareaaddress1}
                            </label>
                            <input type="text" 
                                   name="address1" 
                                   id="inputAddress1" 
                                   value="{$clientsdetails.address1}" 
                                   class="form-control" 
                                   required>
                        </div>

                        <div class="form-group">
                            <label for="inputAddress2" class="form-label">
                                {$LANG.clientareaaddress2}
                            </label>
                            <input type="text" 
                                   name="address2" 
                                   id="inputAddress2" 
                                   value="{$clientsdetails.address2}" 
                                   class="form-control">
                        </div>

                        <div class="form-group">
                            <label for="inputCity" class="form-label required">
                                {$LANG.clientareacity}
                            </label>
                            <input type="text" 
                                   name="city" 
                                   id="inputCity" 
                                   value="{$clientsdetails.city}" 
                                   class="form-control" 
                                   required>
                        </div>

                        <div class="form-group">
                            <label for="inputState" class="form-label required">
                                {$LANG.clientareastate}
                            </label>
                            <input type="text" 
                                   name="state" 
                                   id="inputState" 
                                   value="{$clientsdetails.state}" 
                                   class="form-control" 
                                   required>
                        </div>

                        <div class="form-group">
                            <label for="inputPostcode" class="form-label required">
                                {$LANG.clientareapostcode}
                            </label>
                            <input type="text" 
                                   name="postcode" 
                                   id="inputPostcode" 
                                   value="{$clientsdetails.postcode}" 
                                   class="form-control" 
                                   required>
                        </div>

                        <div class="form-group">
                            <label for="inputCountry" class="form-label required">
                                {$LANG.clientareacountry}
                            </label>
                            <select name="country" id="inputCountry" class="form-control" required>
                                <option value="">{$LANG.choosecountry}</option>
                                {foreach from=$countries key=code item=countryname}
                                    <option value="{$code}"{if $clientsdetails.country eq $code} selected{/if}>
                                        {$countryname}
                                    </option>
                                {/foreach}
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="inputPhone" class="form-label required">
                                {$LANG.clientareaphonenumber}
                            </label>
                            <input type="tel" 
                                   name="phonenumber" 
                                   id="inputPhone" 
                                   value="{$clientsdetails.phonenumber}" 
                                   class="form-control" 
                                   required>
                        </div>

                        <!-- Account Security -->
                        <div class="md:col-span-2 mt-6">
                            <h3 class="text-xl font-semibold text-white mb-4 flex items-center">
                                <i data-lucide="shield" class="w-5 h-5 mr-2 text-green-400"></i>
                                Account Security
                            </h3>
                        </div>

                        <div class="form-group">
                            <label for="inputNewPassword1" class="form-label required">
                                {$LANG.clientareapassword}
                            </label>
                            <input type="password" 
                                   name="password" 
                                   id="inputNewPassword1" 
                                   class="form-control" 
                                   required>
                            <div class="text-sm text-gray-400 mt-1">
                                Minimum 8 characters with uppercase, lowercase, and numbers
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="inputNewPassword2" class="form-label required">
                                {$LANG.clientareaconfirmpassword}
                            </label>
                            <input type="password" 
                                   name="password2" 
                                   id="inputNewPassword2" 
                                   class="form-control" 
                                   required>
                        </div>
                    </div>

                    {if $customfields}
                        <div class="mt-8">
                            <h3 class="text-xl font-semibold text-white mb-4 flex items-center">
                                <i data-lucide="settings" class="w-5 h-5 mr-2 text-orange-400"></i>
                                Additional Information
                            </h3>
                            {include file="$template/includes/customfields.tpl" customfields=$customfields}
                        </div>
                    {/if}

                    {if $captcha}
                        <div class="mt-6">
                            <label class="form-label">
                                <i data-lucide="shield-check" class="w-4 h-4 mr-2 inline"></i>
                                {$LANG.captchaverify}
                            </label>
                            <div class="captcha-container">
                                {$captcha}
                            </div>
                        </div>
                    {/if}

                    <div class="mt-8">
                        <label class="flex items-start">
                            <input type="checkbox" name="accepttos" class="mt-1 mr-3" required>
                            <span class="text-sm text-gray-300">
                                I agree to the 
                                <a href="{$WEB_ROOT}/terms.php" target="_blank" class="text-blue-400 hover:text-blue-300">
                                    Terms of Service
                                </a> 
                                and 
                                <a href="{$WEB_ROOT}/privacy.php" target="_blank" class="text-blue-400 hover:text-blue-300">
                                    Privacy Policy
                                </a>
                            </span>
                        </label>
                    </div>

                    <div class="mt-8 flex flex-col sm:flex-row gap-4">
                        <button type="submit" class="btn btn-primary flex-1">
                            <i data-lucide="user-plus" class="w-4 h-4 mr-2"></i>
                            {$LANG.orderForm.createAccount}
                        </button>
                        <a href="login.php" class="btn btn-secondary flex-1 text-center">
                            <i data-lucide="log-in" class="w-4 h-4 mr-2"></i>
                            {$LANG.existingcustomer}
                        </a>
                    </div>
                </form>
            </div>
        </div>

        <!-- Trust Indicators -->
        <div class="mt-8 grid grid-cols-1 md:grid-cols-3 gap-4">
            <div class="text-center p-4 bg-gradient-to-r from-blue-500/10 to-purple-500/10 border border-blue-500/20 rounded-lg">
                <i data-lucide="shield-check" class="w-8 h-8 text-green-400 mx-auto mb-2"></i>
                <h4 class="font-semibold text-white mb-1">Secure & Private</h4>
                <p class="text-sm text-gray-400">Your data is protected with enterprise-grade security</p>
            </div>
            <div class="text-center p-4 bg-gradient-to-r from-purple-500/10 to-pink-500/10 border border-purple-500/20 rounded-lg">
                <i data-lucide="zap" class="w-8 h-8 text-yellow-400 mx-auto mb-2"></i>
                <h4 class="font-semibold text-white mb-1">Instant Setup</h4>
                <p class="text-sm text-gray-400">Get started immediately after registration</p>
            </div>
            <div class="text-center p-4 bg-gradient-to-r from-pink-500/10 to-red-500/10 border border-pink-500/20 rounded-lg">
                <i data-lucide="headphones" class="w-8 h-8 text-blue-400 mx-auto mb-2"></i>
                <h4 class="font-semibold text-white mb-1">24/7 Support</h4>
                <p class="text-sm text-gray-400">Expert help whenever you need it</p>
            </div>
        </div>
    </div>
</div>

{include file="$template/footer.tpl"}
