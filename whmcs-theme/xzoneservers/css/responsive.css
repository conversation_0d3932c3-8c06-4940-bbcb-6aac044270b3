/**
 * X-ZoneServers WHMCS Theme - Responsive Styles
 * Mobile-first responsive design for all screen sizes
 */

/* Mobile First Approach */
/* Base styles are for mobile (320px+) */

/* Small devices (landscape phones, 576px and up) */
@media (min-width: 576px) {
    .container {
        max-width: 540px;
    }
    
    .pricing-card {
        padding: var(--xz-spacing-xl);
    }
    
    .btn-lg {
        padding: var(--xz-spacing-lg) var(--xz-spacing-2xl);
    }
}

/* Medium devices (tablets, 768px and up) */
@media (min-width: 768px) {
    .container {
        max-width: 720px;
    }
    
    /* Grid layouts for tablets */
    .row {
        display: flex;
        flex-wrap: wrap;
        margin: 0 -15px;
    }
    
    .col-md-1 { flex: 0 0 8.333333%; max-width: 8.333333%; }
    .col-md-2 { flex: 0 0 16.666667%; max-width: 16.666667%; }
    .col-md-3 { flex: 0 0 25%; max-width: 25%; }
    .col-md-4 { flex: 0 0 33.333333%; max-width: 33.333333%; }
    .col-md-5 { flex: 0 0 41.666667%; max-width: 41.666667%; }
    .col-md-6 { flex: 0 0 50%; max-width: 50%; }
    .col-md-7 { flex: 0 0 58.333333%; max-width: 58.333333%; }
    .col-md-8 { flex: 0 0 66.666667%; max-width: 66.666667%; }
    .col-md-9 { flex: 0 0 75%; max-width: 75%; }
    .col-md-10 { flex: 0 0 83.333333%; max-width: 83.333333%; }
    .col-md-11 { flex: 0 0 91.666667%; max-width: 91.666667%; }
    .col-md-12 { flex: 0 0 100%; max-width: 100%; }
    
    [class*="col-"] {
        padding: 0 15px;
    }
    
    /* Table responsive */
    .table-responsive {
        overflow-x: visible;
    }
    
    /* Modal adjustments */
    .modal-dialog {
        max-width: 600px;
        margin: 30px auto;
    }
    
    /* Navigation improvements */
    .navbar-nav {
        flex-direction: row;
    }
    
    .navbar-nav .nav-item {
        margin-right: var(--xz-spacing-md);
    }
}

/* Large devices (desktops, 992px and up) */
@media (min-width: 992px) {
    .container {
        max-width: 960px;
    }
    
    /* Large grid columns */
    .col-lg-1 { flex: 0 0 8.333333%; max-width: 8.333333%; }
    .col-lg-2 { flex: 0 0 16.666667%; max-width: 16.666667%; }
    .col-lg-3 { flex: 0 0 25%; max-width: 25%; }
    .col-lg-4 { flex: 0 0 33.333333%; max-width: 33.333333%; }
    .col-lg-5 { flex: 0 0 41.666667%; max-width: 41.666667%; }
    .col-lg-6 { flex: 0 0 50%; max-width: 50%; }
    .col-lg-7 { flex: 0 0 58.333333%; max-width: 58.333333%; }
    .col-lg-8 { flex: 0 0 66.666667%; max-width: 66.666667%; }
    .col-lg-9 { flex: 0 0 75%; max-width: 75%; }
    .col-lg-10 { flex: 0 0 83.333333%; max-width: 83.333333%; }
    .col-lg-11 { flex: 0 0 91.666667%; max-width: 91.666667%; }
    .col-lg-12 { flex: 0 0 100%; max-width: 100%; }
    
    /* Sidebar layout */
    .sidebar {
        width: 280px;
        position: fixed;
        top: 0;
        left: 0;
        height: 100vh;
        background: var(--xz-bg-secondary);
        border-right: 1px solid var(--xz-border);
        padding: var(--xz-spacing-lg);
        overflow-y: auto;
    }
    
    .main-content {
        margin-left: 280px;
        padding: var(--xz-spacing-lg);
    }
    
    /* Pricing cards in grid */
    .pricing-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: var(--xz-spacing-xl);
        margin: var(--xz-spacing-2xl) 0;
    }
    
    /* Enhanced hover effects for desktop */
    .pricing-card:hover {
        transform: translateY(-8px) scale(1.02);
    }
    
    .btn:hover {
        transform: translateY(-2px);
    }
    
    /* Modal improvements */
    .modal-dialog {
        max-width: 800px;
    }
    
    .modal-lg {
        max-width: 1000px;
    }
}

/* Extra large devices (large desktops, 1200px and up) */
@media (min-width: 1200px) {
    .container {
        max-width: 1140px;
    }
    
    /* Extra large grid columns */
    .col-xl-1 { flex: 0 0 8.333333%; max-width: 8.333333%; }
    .col-xl-2 { flex: 0 0 16.666667%; max-width: 16.666667%; }
    .col-xl-3 { flex: 0 0 25%; max-width: 25%; }
    .col-xl-4 { flex: 0 0 33.333333%; max-width: 33.333333%; }
    .col-xl-5 { flex: 0 0 41.666667%; max-width: 41.666667%; }
    .col-xl-6 { flex: 0 0 50%; max-width: 50%; }
    .col-xl-7 { flex: 0 0 58.333333%; max-width: 58.333333%; }
    .col-xl-8 { flex: 0 0 66.666667%; max-width: 66.666667%; }
    .col-xl-9 { flex: 0 0 75%; max-width: 75%; }
    .col-xl-10 { flex: 0 0 83.333333%; max-width: 83.333333%; }
    .col-xl-11 { flex: 0 0 91.666667%; max-width: 91.666667%; }
    .col-xl-12 { flex: 0 0 100%; max-width: 100%; }
    
    /* Wider pricing grid */
    .pricing-grid {
        grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
        gap: var(--xz-spacing-2xl);
    }
    
    /* Enhanced typography for large screens */
    h1 { font-size: 3.5rem; }
    h2 { font-size: 2.5rem; }
    
    .pricing-price {
        font-size: 3.5rem;
    }
}

/* Mobile-specific styles */
@media (max-width: 767px) {
    /* Stack everything vertically on mobile */
    .row {
        flex-direction: column;
    }
    
    [class*="col-"] {
        flex: 0 0 100%;
        max-width: 100%;
        margin-bottom: var(--xz-spacing-lg);
    }
    
    /* Mobile navigation */
    .navbar-toggler {
        background: var(--xz-bg-secondary);
        border: 1px solid var(--xz-border);
        border-radius: var(--xz-radius);
        padding: var(--xz-spacing-sm);
    }
    
    .navbar-collapse {
        background: var(--xz-bg-secondary);
        border: 1px solid var(--xz-border);
        border-radius: var(--xz-radius-lg);
        margin-top: var(--xz-spacing-md);
        padding: var(--xz-spacing-lg);
    }
    
    /* Mobile-friendly buttons */
    .btn {
        width: 100%;
        margin-bottom: var(--xz-spacing-sm);
    }
    
    .btn-group .btn {
        width: auto;
    }
    
    /* Mobile tables */
    .table-responsive {
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
    }
    
    .table {
        min-width: 600px;
    }
    
    /* Mobile modals */
    .modal-dialog {
        margin: 10px;
        max-width: calc(100% - 20px);
    }
    
    /* Mobile forms */
    .form-group {
        margin-bottom: var(--xz-spacing-md);
    }
    
    /* Mobile pricing cards */
    .pricing-card {
        margin-bottom: var(--xz-spacing-xl);
        padding: var(--xz-spacing-lg);
    }
    
    .pricing-price {
        font-size: var(--xz-font-size-3xl);
    }
    
    /* Mobile typography */
    h1 { font-size: var(--xz-font-size-3xl); }
    h2 { font-size: var(--xz-font-size-2xl); }
    h3 { font-size: var(--xz-font-size-xl); }
    
    /* Hide desktop-only elements */
    .d-none.d-md-block {
        display: none !important;
    }
    
    /* Show mobile-only elements */
    .d-block.d-md-none {
        display: block !important;
    }
}

/* Print styles */
@media print {
    * {
        background: white !important;
        color: black !important;
        box-shadow: none !important;
    }
    
    .navbar,
    .sidebar,
    .btn,
    .modal {
        display: none !important;
    }
    
    .container {
        max-width: none !important;
        padding: 0 !important;
    }
    
    .panel,
    .card {
        border: 1px solid #ddd !important;
        break-inside: avoid;
    }
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

@media (prefers-contrast: high) {
    :root {
        --xz-border: #ffffff;
        --xz-border-light: #ffffff;
        --xz-text-secondary: #ffffff;
        --xz-text-muted: #cccccc;
    }
}

/* Dark mode support (if system preference) */
@media (prefers-color-scheme: dark) {
    /* Already dark by default, but ensure consistency */
    body {
        background: var(--xz-bg-primary);
        color: var(--xz-text-secondary);
    }
}
