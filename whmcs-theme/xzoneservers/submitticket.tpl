{include file="$template/header.tpl"}

<div class="container mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Submit Ticket Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-white mb-2">
                    <i data-lucide="plus-circle" class="w-8 h-8 mr-3 inline text-green-400"></i>
                    Submit Support Ticket
                </h1>
                <p class="text-gray-300">Get help from our expert support team</p>
            </div>
            <div>
                <a href="supporttickets.php" class="btn btn-secondary">
                    <i data-lucide="arrow-left" class="w-4 h-4 mr-2"></i>
                    View Existing Tickets
                </a>
            </div>
        </div>
    </div>

    {if $errormessage}
        <div class="alert alert-danger mb-6">
            <i data-lucide="alert-circle" class="w-4 h-4 mr-2 inline"></i>
            {$errormessage}
        </div>
    {/if}

    {if $successmessage}
        <div class="alert alert-success mb-6">
            <i data-lucide="check-circle" class="w-4 h-4 mr-2 inline"></i>
            {$successmessage}
        </div>
    {/if}

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Submit Ticket Form -->
        <div class="lg:col-span-2">
            <div class="panel">
                <div class="panel-heading">
                    <h3 class="flex items-center">
                        <i data-lucide="edit" class="w-5 h-5 mr-2 text-blue-400"></i>
                        Ticket Details
                    </h3>
                </div>
                <div class="panel-body">
                    <form method="post" action="{$smarty.server.PHP_SELF}" enctype="multipart/form-data" class="ticket-form">
                        
                        {if !$loggedin}
                            <!-- Guest Information -->
                            <div class="mb-8">
                                <h4 class="text-lg font-semibold text-white mb-4 flex items-center">
                                    <i data-lucide="user" class="w-5 h-5 mr-2 text-purple-400"></i>
                                    Contact Information
                                </h4>
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div class="form-group">
                                        <label for="name" class="form-label required">
                                            {$LANG.supportticketsclientname}
                                        </label>
                                        <input type="text" name="name" id="name" value="{$name}" class="form-control" required>
                                    </div>
                                    <div class="form-group">
                                        <label for="email" class="form-label required">
                                            {$LANG.supportticketsclientemail}
                                        </label>
                                        <input type="email" name="email" id="email" value="{$email}" class="form-control" required>
                                    </div>
                                </div>
                            </div>
                        {/if}

                        <!-- Ticket Information -->
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                            <div class="form-group">
                                <label for="deptid" class="form-label required">
                                    <i data-lucide="folder" class="w-4 h-4 mr-1 inline"></i>
                                    {$LANG.supportticketsdepartment}
                                </label>
                                <select name="deptid" id="deptid" class="form-control" required onchange="updateDepartmentInfo(this)">
                                    <option value="">{$LANG.supportticketsdepartment}</option>
                                    {foreach from=$departments item=department}
                                        <option value="{$department.id}"{if $department.id eq $deptid} selected{/if}>
                                            {$department.name}
                                        </option>
                                    {/foreach}
                                </select>
                            </div>

                            <div class="form-group">
                                <label for="urgency" class="form-label required">
                                    <i data-lucide="flag" class="w-4 h-4 mr-1 inline"></i>
                                    {$LANG.supportticketsticketurgency}
                                </label>
                                <select name="urgency" id="urgency" class="form-control" required>
                                    <option value="">{$LANG.supportticketsticketurgency}</option>
                                    <option value="Low"{if $urgency eq "Low"} selected{/if}>
                                        {$LANG.supportticketslow}
                                    </option>
                                    <option value="Medium"{if $urgency eq "Medium"} selected{/if}>
                                        {$LANG.supportticketsmedium}
                                    </option>
                                    <option value="High"{if $urgency eq "High"} selected{/if}>
                                        {$LANG.supportticketshigh}
                                    </option>
                                </select>
                            </div>
                        </div>

                        {if $relatedservices}
                            <div class="form-group mb-6">
                                <label for="relatedservice" class="form-label">
                                    <i data-lucide="server" class="w-4 h-4 mr-1 inline"></i>
                                    Related Service (Optional)
                                </label>
                                <select name="relatedservice" id="relatedservice" class="form-control">
                                    <option value="">None</option>
                                    {foreach from=$relatedservices item=service}
                                        <option value="{$service.id}"{if $service.id eq $relatedservice} selected{/if}>
                                            {$service.product} - {$service.domain}
                                        </option>
                                    {/foreach}
                                </select>
                            </div>
                        {/if}

                        <div class="form-group mb-6">
                            <label for="subject" class="form-label required">
                                <i data-lucide="type" class="w-4 h-4 mr-1 inline"></i>
                                {$LANG.supportticketsticketsubject}
                            </label>
                            <input type="text" 
                                   name="subject" 
                                   id="subject" 
                                   value="{$subject}" 
                                   class="form-control" 
                                   placeholder="Brief description of your issue"
                                   required>
                        </div>

                        <div class="form-group mb-6">
                            <label for="message" class="form-label required">
                                <i data-lucide="message-square" class="w-4 h-4 mr-1 inline"></i>
                                {$LANG.supportticketsticketmessage}
                            </label>
                            <textarea name="message" 
                                      id="message" 
                                      rows="8" 
                                      class="form-control" 
                                      placeholder="Please provide detailed information about your issue, including any error messages and steps to reproduce the problem."
                                      required>{$message}</textarea>
                            <div class="text-sm text-gray-400 mt-2">
                                <i data-lucide="info" class="w-3 h-3 mr-1 inline"></i>
                                The more details you provide, the faster we can help resolve your issue.
                            </div>
                        </div>

                        <!-- File Attachments -->
                        <div class="form-group mb-6">
                            <label class="form-label">
                                <i data-lucide="paperclip" class="w-4 h-4 mr-1 inline"></i>
                                Attachments (Optional)
                            </label>
                            <div class="space-y-3">
                                {for $i=0 to 4}
                                    <div class="flex items-center">
                                        <input type="file" 
                                               name="attachments[]" 
                                               class="form-control" 
                                               accept=".jpg,.jpeg,.png,.gif,.pdf,.txt,.doc,.docx,.zip">
                                    </div>
                                {/for}
                            </div>
                            <div class="text-sm text-gray-400 mt-2">
                                <i data-lucide="info" class="w-3 h-3 mr-1 inline"></i>
                                Max file size: 10MB per file. Allowed types: images, documents, archives.
                            </div>
                        </div>

                        {if $customfields}
                            <div class="mb-6">
                                <h4 class="text-lg font-semibold text-white mb-4 flex items-center">
                                    <i data-lucide="settings" class="w-5 h-5 mr-2 text-orange-400"></i>
                                    Additional Information
                                </h4>
                                {include file="$template/includes/customfields.tpl" customfields=$customfields}
                            </div>
                        {/if}

                        {if $captcha}
                            <div class="form-group mb-6">
                                <label class="form-label">
                                    <i data-lucide="shield-check" class="w-4 h-4 mr-1 inline"></i>
                                    {$LANG.captchaverify}
                                </label>
                                <div class="captcha-container">
                                    {$captcha}
                                </div>
                            </div>
                        {/if}

                        <div class="flex flex-col sm:flex-row gap-4">
                            <button type="submit" class="btn btn-primary flex-1">
                                <i data-lucide="send" class="w-4 h-4 mr-2"></i>
                                {$LANG.supportticketssubmitticket}
                            </button>
                            <a href="supporttickets.php" class="btn btn-secondary flex-1 text-center">
                                <i data-lucide="x" class="w-4 h-4 mr-2"></i>
                                Cancel
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Support Information Sidebar -->
        <div class="lg:col-span-1">
            <!-- Quick Help -->
            <div class="panel mb-6">
                <div class="panel-heading">
                    <h3 class="flex items-center">
                        <i data-lucide="help-circle" class="w-5 h-5 mr-2 text-yellow-400"></i>
                        Before You Submit
                    </h3>
                </div>
                <div class="panel-body">
                    <div class="space-y-4">
                        <div class="flex items-start">
                            <i data-lucide="search" class="w-5 h-5 text-blue-400 mr-3 mt-0.5"></i>
                            <div>
                                <h4 class="font-semibold text-white mb-1">Check Knowledge Base</h4>
                                <p class="text-sm text-gray-400">Many common issues are already documented.</p>
                                <a href="knowledgebase.php" class="text-blue-400 hover:text-blue-300 text-sm">
                                    Browse Articles →
                                </a>
                            </div>
                        </div>
                        
                        <div class="flex items-start">
                            <i data-lucide="activity" class="w-5 h-5 text-green-400 mr-3 mt-0.5"></i>
                            <div>
                                <h4 class="font-semibold text-white mb-1">System Status</h4>
                                <p class="text-sm text-gray-400">Check if there are any ongoing issues.</p>
                                <a href="serverstatus.php" class="text-green-400 hover:text-green-300 text-sm">
                                    View Status →
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Support Stats -->
            <div class="panel mb-6">
                <div class="panel-heading">
                    <h3 class="flex items-center">
                        <i data-lucide="clock" class="w-5 h-5 mr-2 text-purple-400"></i>
                        Response Times
                    </h3>
                </div>
                <div class="panel-body">
                    <div class="space-y-3">
                        <div class="flex justify-between items-center">
                            <span class="text-gray-300">Critical Issues:</span>
                            <span class="text-red-400 font-semibold">< 15 min</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-gray-300">High Priority:</span>
                            <span class="text-orange-400 font-semibold">< 30 min</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-gray-300">General Support:</span>
                            <span class="text-blue-400 font-semibold">< 2 hours</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-gray-300">Low Priority:</span>
                            <span class="text-green-400 font-semibold">< 24 hours</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Contact Options -->
            <div class="panel">
                <div class="panel-heading">
                    <h3 class="flex items-center">
                        <i data-lucide="phone" class="w-5 h-5 mr-2 text-emerald-400"></i>
                        Other Ways to Reach Us
                    </h3>
                </div>
                <div class="panel-body">
                    <div class="space-y-4">
                        <div class="flex items-center">
                            <i data-lucide="message-circle" class="w-5 h-5 text-blue-400 mr-3"></i>
                            <div>
                                <div class="font-semibold text-white">Live Chat</div>
                                <div class="text-sm text-gray-400">Available 24/7</div>
                            </div>
                        </div>
                        
                        <div class="flex items-center">
                            <i data-lucide="mail" class="w-5 h-5 text-purple-400 mr-3"></i>
                            <div>
                                <div class="font-semibold text-white">Email Support</div>
                                <div class="text-sm text-gray-400"><EMAIL></div>
                            </div>
                        </div>
                        
                        <div class="flex items-center">
                            <i data-lucide="phone" class="w-5 h-5 text-green-400 mr-3"></i>
                            <div>
                                <div class="font-semibold text-white">Emergency Line</div>
                                <div class="text-sm text-gray-400">For critical issues only</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function updateDepartmentInfo(select) {
    // You can add department-specific information or form changes here
    const deptId = select.value;
    // Example: Show/hide certain fields based on department
}

// Form validation
document.addEventListener('DOMContentLoaded', function() {
    const form = document.querySelector('.ticket-form');
    if (form) {
        form.addEventListener('submit', function(e) {
            const subject = document.getElementById('subject').value.trim();
            const message = document.getElementById('message').value.trim();
            
            if (subject.length < 5) {
                e.preventDefault();
                alert('Please provide a more descriptive subject (minimum 5 characters).');
                return false;
            }
            
            if (message.length < 20) {
                e.preventDefault();
                alert('Please provide more details about your issue (minimum 20 characters).');
                return false;
            }
        });
    }
});
</script>

{include file="$template/footer.tpl"}
