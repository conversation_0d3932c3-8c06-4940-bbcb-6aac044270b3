<?php
/**
 * Aggressive Footer Duplication Fix
 * This hook removes duplicate footers by processing the entire page output
 */

if (!defined("WHMCS")) {
    die("This file cannot be accessed directly");
}

// Main hook to fix duplicate footer issue
add_hook('ClientAreaPageOutput', 999, function($vars) {
    $output = $vars['output'];
    
    // Multiple patterns to catch different footer variations
    $patterns = [
        // Standard footer with id="footer"
        '/<footer[^>]*id=["\']footer["\'][^>]*>.*?<\/footer>/s',
        // Footer with class containing "footer"
        '/<footer[^>]*class=["\'][^"\']*footer[^"\']*["\'][^>]*>.*?<\/footer>/s',
        // Any footer element
        '/<footer[^>]*>.*?<\/footer>/s'
    ];
    
    foreach ($patterns as $pattern) {
        // Find all matches
        preg_match_all($pattern, $output, $matches);
        
        if (count($matches[0]) > 1) {
            // Remove the first occurrence, keep the last one
            $output = preg_replace($pattern, '', $output, count($matches[0]) - 1);
            
            // Log the fix
            logActivity("Footer Fix: Removed " . (count($matches[0]) - 1) . " duplicate footer(s)");
            break; // Exit after first successful fix
        }
    }
    
    return array('output' => $output);
});

/*
// Backup hook with different approach - remove by content similarity
add_hook('ClientAreaPageOutput', 998, function($vars) {
    $output = $vars['output'];

    // Look for duplicate footer content patterns
    $duplicatePatterns = [
        // Copyright text duplication
        '/(&copy;[^<]*X-Zone IT S\.R\.L\.[^<]*){2,}/i',
        // Services section duplication
        '/(Services[^<]*Order Services[^<]*My Services[^<]*){2,}/i',
        // Account section duplication
        '/(Account[^<]*Login[^<]*Register[^<]*){2,}/i'
    ];

    foreach ($duplicatePatterns as $pattern) {
        if (preg_match($pattern, $output)) {
            // Find footer sections and remove duplicates
            $footerPattern = '/<footer[^>]*>.*?<\/footer>/s';
            preg_match_all($footerPattern, $output, $matches);

            if (count($matches[0]) > 1) {
                // Remove all but the last footer
                for ($i = 0; $i < count($matches[0]) - 1; $i++) {
                    $output = str_replace($matches[0][$i], '', $output);
                }

                logActivity("Footer Fix: Removed duplicate footer content");
                break;
            }
        }
    }

    return array('output' => $output);
});

// Nuclear option - if all else fails, this will clean up any remaining duplicates
add_hook('ClientAreaPageOutput', 1000, function($vars) {
    $output = $vars['output'];

    // Count all footer elements
    $footerCount = substr_count(strtolower($output), '<footer');

    if ($footerCount > 1) {
        // Find the last footer position
        $lastFooterPos = strrpos($output, '<footer');

        if ($lastFooterPos !== false) {
            // Find the end of the last footer
            $footerEnd = strpos($output, '</footer>', $lastFooterPos);
            if ($footerEnd !== false) {
                $footerEnd += 9; // Length of '</footer>'

                // Extract the last footer
                $lastFooter = substr($output, $lastFooterPos, $footerEnd - $lastFooterPos);

                // Remove all footers
                $output = preg_replace('/<footer[^>]*>.*?<\/footer>/s', '', $output);

                // Add back only the last footer at the end (before </body>)
                $output = str_replace('</body>', $lastFooter . "\n</body>", $output);

                logActivity("Footer Fix: Nuclear option applied - kept only last footer");
            }
        }
    }

    return array('output' => $output);
});
*/

?>
