<?php
/**
 * Hook to remove duplicate footer
 * This hook removes the first occurrence of the footer to prevent duplication
 */

if (!defined("WHMCS")) {
    die("This file cannot be accessed directly");
}

// Hook to remove duplicate footer from client area output
add_hook('ClientAreaPageOutput', 1, function($vars) {
    $output = $vars['output'];
    
    // Find all footer occurrences
    $footerPattern = '/<footer[^>]*id=["\']footer["\'][^>]*>.*?<\/footer>/s';
    
    // Count footer occurrences
    preg_match_all($footerPattern, $output, $matches);
    
    if (count($matches[0]) > 1) {
        // Remove the first footer occurrence
        $output = preg_replace($footerPattern, '', $output, 1);
        
        // Log for debugging
        logActivity("Duplicate footer detected and first occurrence removed");
    }
    
    return array('output' => $output);
});

// Alternative hook for admin area if needed
add_hook('AdminAreaPageOutput', 1, function($vars) {
    $output = $vars['output'];
    
    // Find all footer occurrences
    $footerPattern = '/<footer[^>]*id=["\']footer["\'][^>]*>.*?<\/footer>/s';
    
    // Count footer occurrences
    preg_match_all($footerPattern, $output, $matches);
    
    if (count($matches[0]) > 1) {
        // Remove the first footer occurrence
        $output = preg_replace($footerPattern, '', $output, 1);
        
        // Log for debugging
        logActivity("Admin: Duplicate footer detected and first occurrence removed");
    }
    
    return array('output' => $output);
});

// Hook specifically for template output processing
add_hook('ClientAreaFooterOutput', 1, function($vars) {
    // This hook runs before footer output
    // We can use it to prevent duplicate footer injection
    
    static $footerProcessed = false;
    
    if ($footerProcessed) {
        // If footer already processed, return empty to prevent duplication
        return '';
    }
    
    $footerProcessed = true;
    return $vars;
});

?>
