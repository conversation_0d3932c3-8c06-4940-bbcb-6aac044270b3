{include file="$template/header.tpl"}

<div class="container mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Page Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-white mb-2">My Services</h1>
                <p class="text-gray-300">Manage and monitor all your hosting services</p>
            </div>
            <div>
                <a href="{$WEB_ROOT}/cart.php" class="btn btn-primary">
                    <i data-lucide="plus" class="w-4 h-4 mr-2"></i>
                    Order New Service
                </a>
            </div>
        </div>
    </div>

    <!-- Service Filters -->
    <div class="mb-6">
        <div class="flex flex-wrap gap-2">
            <button class="btn btn-sm {if !$filterStatus}btn-primary{else}btn-secondary{/if}" onclick="filterServices('all')">
                All Services
            </button>
            <button class="btn btn-sm {if $filterStatus eq 'Active'}btn-primary{else}btn-secondary{/if}" onclick="filterServices('active')">
                <i data-lucide="check-circle" class="w-3 h-3 mr-1"></i>
                Active
            </button>
            <button class="btn btn-sm {if $filterStatus eq 'Suspended'}btn-primary{else}btn-secondary{/if}" onclick="filterServices('suspended')">
                <i data-lucide="pause-circle" class="w-3 h-3 mr-1"></i>
                Suspended
            </button>
            <button class="btn btn-sm {if $filterStatus eq 'Terminated'}btn-primary{else}btn-secondary{/if}" onclick="filterServices('terminated')">
                <i data-lucide="x-circle" class="w-3 h-3 mr-1"></i>
                Terminated
            </button>
        </div>
    </div>

    {if $services}
        <!-- Services Grid -->
        <div class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
            {foreach from=$services item=service}
            <div class="panel service-card" data-status="{$service.status|lower}">
                <div class="panel-body">
                    <!-- Service Header -->
                    <div class="flex items-start justify-between mb-4">
                        <div class="flex items-center">
                            <div class="w-12 h-12 bg-gradient-to-r from-blue-500/20 to-purple-500/20 rounded-lg flex items-center justify-center mr-4">
                                {if $service.type eq 'hostingaccount'}
                                    <i data-lucide="server" class="w-6 h-6 text-blue-400"></i>
                                {elseif $service.type eq 'reselleraccount'}
                                    <i data-lucide="users" class="w-6 h-6 text-purple-400"></i>
                                {elseif $service.type eq 'server'}
                                    <i data-lucide="database" class="w-6 h-6 text-orange-400"></i>
                                {else}
                                    <i data-lucide="box" class="w-6 h-6 text-gray-400"></i>
                                {/if}
                            </div>
                            <div>
                                <h3 class="font-semibold text-white text-lg">{$service.product}</h3>
                                <p class="text-gray-400 text-sm">{$service.domain}</p>
                            </div>
                        </div>
                        <span class="badge badge-{$service.status|lower}">
                            {$service.status}
                        </span>
                    </div>

                    <!-- Service Details -->
                    <div class="space-y-3 mb-6">
                        <div class="flex justify-between items-center">
                            <span class="text-gray-400">Service ID:</span>
                            <span class="text-white font-medium">#{$service.id}</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-gray-400">Billing Cycle:</span>
                            <span class="text-white font-medium">{$service.billingcycle}</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-gray-400">Next Due Date:</span>
                            <span class="text-white font-medium">{$service.nextduedate}</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-gray-400">Amount:</span>
                            <span class="text-white font-medium">{$service.amount}</span>
                        </div>
                    </div>

                    <!-- Progress Bar for Active Services -->
                    {if $service.status eq 'Active' && $service.usage}
                    <div class="mb-6">
                        <div class="flex justify-between items-center mb-2">
                            <span class="text-gray-400 text-sm">Resource Usage</span>
                            <span class="text-white text-sm">{$service.usage.percentage}%</span>
                        </div>
                        <div class="progress">
                            <div class="progress-bar" style="width: {$service.usage.percentage}%"></div>
                        </div>
                    </div>
                    {/if}

                    <!-- Action Buttons -->
                    <div class="flex flex-wrap gap-2">
                        <a href="clientarea.php?action=productdetails&id={$service.id}" class="btn btn-primary btn-sm flex-1">
                            <i data-lucide="eye" class="w-3 h-3 mr-1"></i>
                            Manage
                        </a>
                        {if $service.status eq 'Active'}
                            {if $service.serverdata.cpanelurl}
                            <a href="{$service.serverdata.cpanelurl}" target="_blank" class="btn btn-secondary btn-sm">
                                <i data-lucide="external-link" class="w-3 h-3 mr-1"></i>
                                cPanel
                            </a>
                            {/if}
                            {if $service.serverdata.directadminurl}
                            <a href="{$service.serverdata.directadminurl}" target="_blank" class="btn btn-secondary btn-sm">
                                <i data-lucide="external-link" class="w-3 h-3 mr-1"></i>
                                DirectAdmin
                            </a>
                            {/if}
                        {/if}
                    </div>

                    <!-- Quick Stats for Active Services -->
                    {if $service.status eq 'Active' && $service.stats}
                    <div class="mt-4 pt-4 border-t border-slate-700/50">
                        <div class="grid grid-cols-2 gap-4 text-center">
                            <div>
                                <div class="text-lg font-semibold text-white">{$service.stats.diskusage}</div>
                                <div class="text-xs text-gray-400">Disk Used</div>
                            </div>
                            <div>
                                <div class="text-lg font-semibold text-white">{$service.stats.bandwidth}</div>
                                <div class="text-xs text-gray-400">Bandwidth</div>
                            </div>
                        </div>
                    </div>
                    {/if}
                </div>
            </div>
            {/foreach}
        </div>

        <!-- Pagination -->
        {if $pagination}
        <div class="mt-8">
            {$pagination}
        </div>
        {/if}

    {else}
        <!-- Empty State -->
        <div class="panel">
            <div class="panel-body text-center py-16">
                <div class="w-24 h-24 bg-slate-800/50 rounded-full flex items-center justify-center mx-auto mb-6">
                    <i data-lucide="server" class="w-12 h-12 text-gray-400"></i>
                </div>
                <h3 class="text-2xl font-semibold text-white mb-4">No Services Found</h3>
                <p class="text-gray-400 mb-8 max-w-md mx-auto">
                    You don't have any services yet. Get started with our powerful hosting solutions.
                </p>
                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                    <a href="{$WEB_ROOT}/cart.php?gid=1" class="btn btn-primary">
                        <i data-lucide="server" class="w-4 h-4 mr-2"></i>
                        VPS Hosting
                    </a>
                    <a href="{$WEB_ROOT}/cart.php?gid=3" class="btn btn-secondary">
                        <i data-lucide="database" class="w-4 h-4 mr-2"></i>
                        Dedicated Servers
                    </a>
                </div>
            </div>
        </div>
    {/if}

    <!-- Service Management Tips -->
    <div class="mt-8">
        <div class="bg-gradient-to-r from-blue-500/10 to-purple-500/10 border border-blue-500/20 rounded-xl p-6">
            <h3 class="text-xl font-semibold text-white mb-4 flex items-center">
                <i data-lucide="lightbulb" class="w-5 h-5 mr-2 text-yellow-400"></i>
                Service Management Tips
            </h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <div class="flex items-start">
                    <div class="w-8 h-8 bg-blue-500/20 rounded-lg flex items-center justify-center mr-3 mt-1">
                        <i data-lucide="shield-check" class="w-4 h-4 text-blue-400"></i>
                    </div>
                    <div>
                        <h4 class="font-medium text-white mb-1">Keep Services Updated</h4>
                        <p class="text-gray-400 text-sm">Regularly update your services to ensure optimal performance and security.</p>
                    </div>
                </div>
                <div class="flex items-start">
                    <div class="w-8 h-8 bg-emerald-500/20 rounded-lg flex items-center justify-center mr-3 mt-1">
                        <i data-lucide="backup" class="w-4 h-4 text-emerald-400"></i>
                    </div>
                    <div>
                        <h4 class="font-medium text-white mb-1">Regular Backups</h4>
                        <p class="text-gray-400 text-sm">Enable automatic backups to protect your data and ensure business continuity.</p>
                    </div>
                </div>
                <div class="flex items-start">
                    <div class="w-8 h-8 bg-orange-500/20 rounded-lg flex items-center justify-center mr-3 mt-1">
                        <i data-lucide="activity" class="w-4 h-4 text-orange-400"></i>
                    </div>
                    <div>
                        <h4 class="font-medium text-white mb-1">Monitor Usage</h4>
                        <p class="text-gray-400 text-sm">Keep an eye on resource usage to optimize performance and avoid overages.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function filterServices(status) {
    const cards = document.querySelectorAll('.service-card');
    const buttons = document.querySelectorAll('[onclick^="filterServices"]');
    
    // Update button states
    buttons.forEach(btn => {
        btn.classList.remove('btn-primary');
        btn.classList.add('btn-secondary');
    });
    event.target.classList.remove('btn-secondary');
    event.target.classList.add('btn-primary');
    
    // Filter cards
    cards.forEach(card => {
        if (status === 'all' || card.dataset.status === status) {
            card.style.display = 'block';
        } else {
            card.style.display = 'none';
        }
    });
}
</script>

{include file="$template/footer.tpl"}
