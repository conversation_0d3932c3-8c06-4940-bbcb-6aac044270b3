// Header loader script with inline header content
function loadHeader() {
    const headerHTML = `
    <!-- Enhanced Header -->
    <header class="fixed top-0 left-0 right-0 z-50 bg-gradient-to-r from-slate-950/95 via-slate-900/95 to-slate-950/95 backdrop-blur-xl border-b border-slate-800/50">
        <div class="container mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex items-center justify-between h-20">
                <!-- Logo -->
                <a href="index.html" class="text-2xl font-bold text-white flex items-center group">

                    <span class="bg-gradient-to-r from-white to-gray-200 bg-clip-text text-transparent">
                        X-Zone<span class="bg-gradient-to-r from-blue-400 to-purple-500 bg-clip-text text-transparent">Servers</span>
                    </span>
                </a>

                <!-- Desktop Navigation -->
                <nav class="hidden xl:flex items-center space-x-1">
                    <div class="relative group">
                        <button class="group relative px-4 py-2 text-gray-300 hover:text-white transition-all duration-300 nav-link flex items-center" data-page="vps">
                            <span class="relative z-10 flex items-center">
                                <i data-lucide="server" class="w-4 h-4 mr-2"></i>
                                VPS
                                <i data-lucide="chevron-down" class="w-3 h-3 ml-1 transition-transform duration-300 group-hover:rotate-180"></i>
                            </span>
                            <div class="absolute inset-0 bg-gradient-to-r from-blue-500/10 to-purple-500/10 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                        </button>
                        <!-- VPS Dropdown -->
                        <div class="absolute top-full left-0 mt-2 w-48 bg-slate-900/95 backdrop-blur-xl border border-slate-700/50 rounded-xl shadow-xl opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 z-50">
                            <div class="p-2">
                                <a href="shared-vps.html" class="flex items-center px-3 py-2 text-gray-300 hover:text-white hover:bg-gradient-to-r hover:from-blue-500/10 hover:to-purple-500/10 rounded-lg transition-all duration-300">
                                    <i data-lucide="share-2" class="w-4 h-4 mr-3"></i>
                                    Shared VPS
                                </a>
                                <a href="streaming-vps.html" class="flex items-center px-3 py-2 text-gray-300 hover:text-white hover:bg-gradient-to-r hover:from-purple-500/10 hover:to-pink-500/10 rounded-lg transition-all duration-300">
                                    <i data-lucide="video" class="w-4 h-4 mr-3"></i>
                                    Streaming VPS
                                </a>
                            </div>
                        </div>
                    </div>
                    <a href="dedicated.html" class="group relative px-4 py-2 text-gray-300 hover:text-white transition-all duration-300 nav-link" data-page="dedicated">
                        <span class="relative z-10 flex items-center">
                            <i data-lucide="database" class="w-4 h-4 mr-2"></i>
                            Dedicated Servers
                        </span>
                        <div class="absolute inset-0 bg-gradient-to-r from-purple-500/10 to-pink-500/10 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    </a>
                    <a href="game-hosting.html" class="group relative px-4 py-2 text-gray-300 hover:text-white transition-all duration-300 nav-link" data-page="game-hosting">
                        <span class="relative z-10 flex items-center">
                            <i data-lucide="gamepad-2" class="w-4 h-4 mr-2"></i>
                            Game Hosting
                        </span>
                        <div class="absolute inset-0 bg-gradient-to-r from-orange-500/10 to-red-500/10 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    </a>
                    <a href="network.html" class="group relative px-4 py-2 text-gray-300 hover:text-white transition-all duration-300 nav-link" data-page="network">
                        <span class="relative z-10 flex items-center">
                            <i data-lucide="network" class="w-4 h-4 mr-2"></i>
                            Network
                        </span>
                        <div class="absolute inset-0 bg-gradient-to-r from-green-500/10 to-blue-500/10 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    </a>
                    <a href="about.html" class="group relative px-4 py-2 text-gray-300 hover:text-white transition-all duration-300 nav-link" data-page="about">
                        <span class="relative z-10 flex items-center">
                            <i data-lucide="users" class="w-4 h-4 mr-2"></i>
                            About Us
                        </span>
                        <div class="absolute inset-0 bg-gradient-to-r from-pink-500/10 to-orange-500/10 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    </a>
                    <a href="contact.html" class="group relative px-4 py-2 text-gray-300 hover:text-white transition-all duration-300 nav-link" data-page="contact">
                        <span class="relative z-10 flex items-center">
                            <i data-lucide="phone" class="w-4 h-4 mr-2"></i>
                            Contact
                        </span>
                        <div class="absolute inset-0 bg-gradient-to-r from-teal-500/10 to-cyan-500/10 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    </a>
                </nav>

                <!-- CTA Button -->
                <a href="https://x-zoneservers.com/whmcs/index.php/login" class="hidden xl:inline-flex items-center px-6 py-2.5 bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 text-white font-semibold rounded-xl hover:shadow-lg hover:shadow-blue-500/25 transition-all duration-300 transform hover:scale-105" id="header-cta">
                    <i data-lucide="user" class="w-4 h-4 mr-2"></i>
                    Client Area
                </a>

                <!-- Mobile Menu Button -->
                <button id="mobile-menu-button" class="xl:hidden p-2 text-white hover:bg-slate-800/50 rounded-lg transition-colors duration-300 relative">
                    <div class="w-6 h-6 flex flex-col justify-center items-center">
                        <span class="hamburger-line block w-5 h-0.5 bg-white transition-all duration-300 ease-in-out"></span>
                        <span class="hamburger-line block w-5 h-0.5 bg-white mt-1 transition-all duration-300 ease-in-out"></span>
                        <span class="hamburger-line block w-5 h-0.5 bg-white mt-1 transition-all duration-300 ease-in-out"></span>
                    </div>
                </button>
            </div>
        </div>

        <!-- Enhanced Mobile Menu -->
        <div id="mobile-menu" class="hidden xl:hidden bg-gradient-to-br from-slate-950/98 to-slate-900/98 backdrop-blur-xl border-t border-slate-800/50">
            <div class="px-4 py-6 space-y-3">
                <div class="vps-mobile-dropdown">
                    <button class="flex items-center justify-between w-full px-4 py-3 text-gray-300 hover:text-white hover:bg-gradient-to-r hover:from-blue-500/10 hover:to-purple-500/10 rounded-xl transition-all duration-300 mobile-nav-link" data-page="vps" id="mobile-vps-toggle">
                        <div class="flex items-center">
                            <i data-lucide="server" class="w-5 h-5 mr-3"></i>
                            VPS
                        </div>
                        <i data-lucide="chevron-down" class="w-4 h-4 transition-transform duration-300" id="mobile-vps-chevron"></i>
                    </button>
                    <div class="mobile-vps-submenu hidden mt-2 ml-4 space-y-2">
                        <a href="shared-vps.html" class="flex items-center px-4 py-2 text-gray-400 hover:text-white hover:bg-gradient-to-r hover:from-blue-500/10 hover:to-purple-500/10 rounded-lg transition-all duration-300">
                            <i data-lucide="share-2" class="w-4 h-4 mr-3"></i>
                            Shared VPS
                        </a>
                        <a href="streaming-vps.html" class="flex items-center px-4 py-2 text-gray-400 hover:text-white hover:bg-gradient-to-r hover:from-purple-500/10 hover:to-pink-500/10 rounded-lg transition-all duration-300">
                            <i data-lucide="video" class="w-4 h-4 mr-3"></i>
                            Streaming VPS
                        </a>
                    </div>
                </div>
                <a href="dedicated.html" class="flex items-center px-4 py-3 text-gray-300 hover:text-white hover:bg-gradient-to-r hover:from-purple-500/10 hover:to-pink-500/10 rounded-xl transition-all duration-300 mobile-nav-link" data-page="dedicated">
                    <i data-lucide="database" class="w-5 h-5 mr-3"></i>
                    Dedicated Servers
                </a>
                <a href="game-hosting.html" class="flex items-center px-4 py-3 text-gray-300 hover:text-white hover:bg-gradient-to-r hover:from-orange-500/10 hover:to-red-500/10 rounded-xl transition-all duration-300 mobile-nav-link" data-page="game-hosting">
                    <i data-lucide="gamepad-2" class="w-5 h-5 mr-3"></i>
                    Game Hosting
                </a>
                <a href="network.html" class="flex items-center px-4 py-3 text-gray-300 hover:text-white hover:bg-gradient-to-r hover:from-green-500/10 hover:to-blue-500/10 rounded-xl transition-all duration-300 mobile-nav-link" data-page="network">
                    <i data-lucide="network" class="w-5 h-5 mr-3"></i>
                    Network
                </a>
                <a href="about.html" class="flex items-center px-4 py-3 text-gray-300 hover:text-white hover:bg-gradient-to-r hover:from-pink-500/10 hover:to-orange-500/10 rounded-xl transition-all duration-300 mobile-nav-link" data-page="about">
                    <i data-lucide="users" class="w-5 h-5 mr-3"></i>
                    About Us
                </a>
                <a href="contact.html" class="flex items-center px-4 py-3 text-gray-300 hover:text-white hover:bg-gradient-to-r hover:from-teal-500/10 hover:to-cyan-500/10 rounded-xl transition-all duration-300 mobile-nav-link" data-page="contact">
                    <i data-lucide="phone" class="w-5 h-5 mr-3"></i>
                    Contact
                </a>
                <div class="pt-4 border-t border-slate-800/50">
                    <a href="https://x-zoneservers.com/whmcs/index.php/login" class="flex items-center justify-center w-full px-6 py-3 bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 text-white font-semibold rounded-xl hover:shadow-lg hover:shadow-blue-500/25 transition-all duration-300" id="mobile-header-cta">
                        <i data-lucide="user" class="w-4 h-4 mr-2"></i>
                        Client Area
                    </a>
                </div>
            </div>
        </div>
    </header>
    `;

    document.getElementById('header-placeholder').innerHTML = headerHTML;

    // Initialize header functionality after loading
    initializeHeader();
}

function initializeHeader() {
    // Mobile menu toggle
    const mobileMenuButton = document.getElementById('mobile-menu-button');
    const mobileMenu = document.getElementById('mobile-menu');
    const hamburgerLines = document.querySelectorAll('.hamburger-line');

    if (mobileMenuButton && mobileMenu) {
        let isOpen = false;
        
        mobileMenuButton.addEventListener('click', () => {
            isOpen = !isOpen;
            mobileMenu.classList.toggle('hidden');
            
            // Animate hamburger to X
            if (isOpen) {
                hamburgerLines[0].style.transform = 'rotate(45deg) translate(6px, 6px)';
                hamburgerLines[1].style.opacity = '0';
                hamburgerLines[2].style.transform = 'rotate(-45deg) translate(6px, -6px)';
            } else {
                hamburgerLines[0].style.transform = 'rotate(0) translate(0, 0)';
                hamburgerLines[1].style.opacity = '1';
                hamburgerLines[2].style.transform = 'rotate(0) translate(0, 0)';
            }
        });
        
        // Close mobile menu when clicking outside
        document.addEventListener('click', (e) => {
            if (!mobileMenuButton.contains(e.target) && !mobileMenu.contains(e.target) && !mobileMenu.classList.contains('hidden')) {
                isOpen = false;
                mobileMenu.classList.add('hidden');
                hamburgerLines[0].style.transform = 'rotate(0) translate(0, 0)';
                hamburgerLines[1].style.opacity = '1';
                hamburgerLines[2].style.transform = 'rotate(0) translate(0, 0)';
            }
        });
    }

    // Mobile VPS dropdown toggle
    const mobileVpsToggle = document.getElementById('mobile-vps-toggle');
    const mobileVpsSubmenu = document.querySelector('.mobile-vps-submenu');
    const mobileVpsChevron = document.getElementById('mobile-vps-chevron');

    if (mobileVpsToggle && mobileVpsSubmenu && mobileVpsChevron) {
        mobileVpsToggle.addEventListener('click', () => {
            mobileVpsSubmenu.classList.toggle('hidden');
            mobileVpsChevron.classList.toggle('rotate-180');
        });
    }

    // Set active navigation based on current page
    const currentPage = window.location.pathname.split('/').pop().replace('.html', '') || 'index';
    const navLinks = document.querySelectorAll('.nav-link, .mobile-nav-link');

    navLinks.forEach(link => {
        const linkPage = link.getAttribute('data-page');
        if (currentPage === linkPage) {
            // Active page styling
            link.classList.remove('text-gray-300');
            link.classList.add('text-white');
            // Add active background for desktop
            if (link.classList.contains('nav-link')) {
                const bgDiv = link.querySelector('div');
                if (bgDiv) {
                    bgDiv.classList.remove('opacity-0');
                    bgDiv.classList.add('opacity-100');
                }
            }
            // Add active background for mobile
            if (link.classList.contains('mobile-nav-link')) {
                link.classList.add('bg-gradient-to-r', 'from-blue-500/20', 'to-purple-500/20');
            }
        }
    });


    // Re-initialize Lucide icons for dynamically added content
    if (typeof lucide !== 'undefined') {
        lucide.createIcons();
    }
}

// Load header when DOM is ready
document.addEventListener('DOMContentLoaded', loadHeader);
