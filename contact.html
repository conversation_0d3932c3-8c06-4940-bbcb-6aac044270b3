<!DOCTYPE html>
<html lang="en" class="scroll-smooth">
<head>
    <!-- Skip Link for Accessibility -->
    <a href="#main-content" class="skip-link">Skip to main content</a>
    
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Contact X-ZoneServers - Enterprise Hosting Support & Sales | 24/7 Expert Assistance</title>
    
    <!-- SEO Meta Tags -->
    <meta name="description" content="Contact X-ZoneServers for enterprise hosting support, sales inquiries, and technical assistance. 24/7 expert support, multiple contact channels, and rapid response times.">
    <meta name="keywords" content="contact hosting support, enterprise server support, 24/7 technical assistance, hosting sales inquiry, server support contact, dedicated server help">
    <meta name="robots" content="index, follow, max-image-preview:large, max-snippet:-1, max-video-preview:-1">
    <meta name="author" content="X-ZoneServers">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="Contact X-ZoneServers - Enterprise Hosting Support & Sales">
    <meta property="og:description" content="Get in touch with our hosting experts. 24/7 support, sales inquiries, and technical assistance for all your server needs.">
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://x-zoneservers.com/contact.html">
    <meta property="og:image" content="https://x-zoneservers.com/images/contact-support-team.jpg">
    <meta property="og:site_name" content="X-ZoneServers">
    <meta property="og:locale" content="en_US">
    
    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="Contact X-ZoneServers - Enterprise Hosting Support">
    <meta name="twitter:description" content="24/7 expert hosting support and sales assistance. Contact our team today.">
    <meta name="twitter:image" content="https://x-zoneservers.com/images/contact-support-team.jpg">
    
    <!-- Advanced SEO Meta Tags -->
    <meta name="geo.region" content="EU">
    <meta name="geo.placename" content="Europe">
    <meta name="ICBM" content="52.3676, 4.9041">
    <meta name="language" content="English">
    <meta name="coverage" content="Worldwide">
    <meta name="distribution" content="Global">
    <meta name="rating" content="General">
    <meta name="revisit-after" content="7 days">
    
    <!-- Google 2025 AI Ranking Signals -->
    <meta name="neural-intent-primary" content="contact enterprise hosting provider, technical support inquiry, dedicated server assistance, hosting sales consultation">
    <meta name="neural-intent-secondary" content="24/7 server support, enterprise hosting contact, technical assistance request, hosting expert consultation">
    <meta name="ml-optimization" content="contact form optimization, support channel preference, inquiry categorization, response time prediction">
    <meta name="voice-search-queries" content="how to contact X-ZoneServers, enterprise hosting support phone number, dedicated server technical help, hosting sales contact information">
    <meta name="search-intent-clusters" content="support contact, sales inquiry, technical assistance, enterprise hosting help, server management support">
    <meta name="semantic-entities" content="Contact Information, Technical Support, Sales Team, Enterprise Hosting, Customer Service">
    <meta name="user-journey-stage" content="consideration, decision, support">
    <meta name="content-freshness-signal" content="contact-information-current">
    <meta name="expertise-indicators" content="enterprise support team, certified technicians, 24/7 availability, rapid response">
    
    <!-- Canonical URL -->
    <link rel="canonical" href="https://x-zoneservers.com/contact.html">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png">
    
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;900&display=swap" rel="stylesheet">
    <script src="https://unpkg.com/lucide@latest"></script>
    <link rel="stylesheet" href="style.css">
    
    <!-- Structured Data - Organization Contact -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "Organization",
      "@id": "https://x-zoneservers.com/#organization",
      "name": "X-ZoneServers",
      "alternateName": "X-Zone Servers",
      "url": "https://x-zoneservers.com",
      "logo": "https://x-zoneservers.com/images/x-zoneservers-logo.png",
      "contactPoint": [
        {
          "@type": "ContactPoint",
          "telephone": "+31-20-123-4567",
          "contactType": "customer service",
          "availableLanguage": ["English", "Dutch", "German", "French"],
          "hoursAvailable": "24/7"
        },
        {
          "@type": "ContactPoint",
          "telephone": "+31-20-123-4568",
          "contactType": "technical support",
          "availableLanguage": ["English", "Dutch", "German"],
          "hoursAvailable": "24/7"
        },
        {
          "@type": "ContactPoint",
          "telephone": "+31-20-123-4569",
          "contactType": "sales",
          "availableLanguage": ["English", "Dutch", "German", "French"],
          "hoursAvailable": "Mo-Fr 09:00-18:00"
        }
      ],
      "address": {
        "@type": "PostalAddress",
        "streetAddress": "Tech Tower, Zuiderpark 15",
        "addressLocality": "Amsterdam",
        "addressRegion": "North Holland",
        "postalCode": "1082 MN",
        "addressCountry": "NL"
      },
      "sameAs": [
        "https://www.linkedin.com/company/x-zoneservers",
        "https://twitter.com/xzoneservers",
        "https://www.facebook.com/xzoneservers"
      ]
    }
    </script>
    
    <!-- Structured Data - ContactPage -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "ContactPage",
      "name": "Contact X-ZoneServers",
      "description": "Contact page for X-ZoneServers enterprise hosting provider with support, sales, and technical assistance information.",
      "url": "https://x-zoneservers.com/contact.html",
      "mainEntity": {
        "@type": "Organization",
        "@id": "https://x-zoneservers.com/#organization"
      },
      "breadcrumb": {
        "@type": "BreadcrumbList",
        "itemListElement": [
          {
            "@type": "ListItem",
            "position": 1,
            "name": "Home",
            "item": "https://x-zoneservers.com/"
          },
          {
            "@type": "ListItem",
            "position": 2,
            "name": "Contact",
            "item": "https://x-zoneservers.com/contact.html"
          }
        ]
      }
    }
    </script>
</head>
<body class="bg-slate-950 text-gray-300">
    <!-- Header Placeholder -->
    <div id="header-placeholder"></div>

    <main id="main-content">
        <!-- Hero Section -->
        <section class="pt-32 pb-16 bg-gradient-to-br from-slate-950 via-slate-900 to-slate-950 relative overflow-hidden">
            <div class="absolute inset-0 hero-gradient"></div>
            <div class="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
                <div class="text-center max-w-4xl mx-auto">
                    <h1 class="text-4xl md:text-6xl font-bold mb-6">
                        <span class="bg-gradient-to-r from-blue-400 via-purple-500 to-pink-500 bg-clip-text text-transparent">Contact Our</span>
                        <br class="hidden sm:block">
                        <span class="text-white">Expert Team</span>
                    </h1>
                    <p class="text-xl text-gray-300 mb-8 leading-relaxed">
                        Get in touch with our hosting specialists for enterprise solutions, technical support, or sales inquiries. 
                        <strong class="text-white">24/7 availability</strong> ensures you're never alone.
                    </p>
                    
                    <!-- Quick Contact Stats -->
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mt-12">
                        <div class="bg-slate-900/50 backdrop-blur-sm rounded-xl p-6 border border-slate-800/50">
                            <div class="w-12 h-12 bg-gradient-to-r from-green-400 to-emerald-500 rounded-lg flex items-center justify-center mx-auto mb-4">
                                <i data-lucide="clock" class="w-6 h-6 text-white"></i>
                            </div>
                            <h3 class="text-xl font-semibold text-white mb-2">24/7 Support</h3>
                            <p class="text-gray-400">Round-the-clock technical assistance</p>
                        </div>
                        <div class="bg-slate-900/50 backdrop-blur-sm rounded-xl p-6 border border-slate-800/50">
                            <div class="w-12 h-12 bg-gradient-to-r from-blue-400 to-cyan-500 rounded-lg flex items-center justify-center mx-auto mb-4">
                                <i data-lucide="zap" class="w-6 h-6 text-white"></i>
                            </div>
                            <h3 class="text-xl font-semibold text-white mb-2">&lt; 15min Response</h3>
                            <p class="text-gray-400">Lightning-fast response times</p>
                        </div>
                        <div class="bg-slate-900/50 backdrop-blur-sm rounded-xl p-6 border border-slate-800/50">
                            <div class="w-12 h-12 bg-gradient-to-r from-purple-400 to-violet-500 rounded-lg flex items-center justify-center mx-auto mb-4">
                                <i data-lucide="users" class="w-6 h-6 text-white"></i>
                            </div>
                            <h3 class="text-xl font-semibold text-white mb-2">Expert Team</h3>
                            <p class="text-gray-400">Certified hosting specialists</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Contact Methods Section -->
        <section class="py-20 bg-slate-950/50">
            <div class="container mx-auto px-4 sm:px-6 lg:px-8">
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-12">
                    <!-- Contact Form -->
                    <div class="bg-slate-900/50 backdrop-blur-sm rounded-2xl p-8 border border-slate-800/50">
                        <h2 class="text-3xl font-bold text-white mb-6">
                            <span class="bg-gradient-to-r from-blue-400 to-purple-500 bg-clip-text text-transparent">Send us a message</span>
                        </h2>
                        
                        <form id="contact-form" class="space-y-6" action="#" method="POST">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label for="first-name" class="block text-sm font-medium text-gray-300 mb-2">First Name *</label>
                                    <input type="text" id="first-name" name="first-name" required 
                                           class="w-full px-4 py-3 bg-slate-800/50 border border-slate-700 rounded-lg text-white placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300" 
                                           placeholder="John">
                                </div>
                                <div>
                                    <label for="last-name" class="block text-sm font-medium text-gray-300 mb-2">Last Name *</label>
                                    <input type="text" id="last-name" name="last-name" required 
                                           class="w-full px-4 py-3 bg-slate-800/50 border border-slate-700 rounded-lg text-white placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300" 
                                           placeholder="Doe">
                                </div>
                            </div>
                            
                            <div>
                                <label for="email" class="block text-sm font-medium text-gray-300 mb-2">Email Address *</label>
                                <input type="email" id="email" name="email" required 
                                       class="w-full px-4 py-3 bg-slate-800/50 border border-slate-700 rounded-lg text-white placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300" 
                                       placeholder="<EMAIL>">
                            </div>
                            
                            <div>
                                <label for="phone" class="block text-sm font-medium text-gray-300 mb-2">Phone Number</label>
                                <input type="tel" id="phone" name="phone" 
                                       class="w-full px-4 py-3 bg-slate-800/50 border border-slate-700 rounded-lg text-white placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300" 
                                       placeholder="+31 20 123 4567">
                            </div>
                            
                            <div>
                                <label for="subject" class="block text-sm font-medium text-gray-300 mb-2">Subject *</label>
                                <select id="subject" name="subject" required 
                                        class="w-full px-4 py-3 bg-slate-800/50 border border-slate-700 rounded-lg text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300">
                                    <option value="">Select a topic</option>
                                    <option value="sales">Sales Inquiry</option>
                                    <option value="technical">Technical Support</option>
                                    <option value="billing">Billing Question</option>
                                    <option value="partnership">Partnership Opportunity</option>
                                    <option value="other">Other</option>
                                </select>
                            </div>
                            
                            <div>
                                <label for="message" class="block text-sm font-medium text-gray-300 mb-2">Message *</label>
                                <textarea id="message" name="message" rows="5" required 
                                          class="w-full px-4 py-3 bg-slate-800/50 border border-slate-700 rounded-lg text-white placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300 resize-vertical" 
                                          placeholder="Tell us about your hosting needs or how we can help you..."></textarea>
                            </div>
                            
                            <div class="flex items-center">
                                <input type="checkbox" id="privacy-consent" name="privacy-consent" required 
                                       class="w-4 h-4 text-blue-600 bg-slate-800 border-slate-600 rounded focus:ring-blue-500 focus:ring-2">
                                <label for="privacy-consent" class="ml-2 text-sm text-gray-400">
                                    I agree to the <a href="privacy-policy.html" class="text-blue-400 hover:text-blue-300 underline">Privacy Policy</a> and consent to data processing
                                </label>
                            </div>
                            
                            <button type="submit" 
                                    class="w-full bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 text-white font-semibold py-3 px-6 rounded-lg hover:shadow-lg hover:shadow-blue-500/25 transition-all duration-300 transform hover:scale-105">
                                <i data-lucide="send" class="w-4 h-4 mr-2 inline"></i>
                                Send Message
                            </button>
                        </form>
                    </div>
                    
                    <!-- Contact Information -->
                    <div class="space-y-8">
                        <!-- Direct Contact -->
                        <div class="bg-slate-900/50 backdrop-blur-sm rounded-2xl p-8 border border-slate-800/50">
                            <h3 class="text-2xl font-bold text-white mb-6">Direct Contact</h3>
                            
                            <div class="space-y-6">
                     
                                
                                <div class="flex items-start space-x-4">
                                    <div class="w-12 h-12 bg-gradient-to-r from-blue-400 to-cyan-500 rounded-lg flex items-center justify-center flex-shrink-0">
                                        <i data-lucide="mail" class="w-6 h-6 text-white"></i>
                                    </div>
                                    <div>
                                        <h4 class="text-lg font-semibold text-white">Email Support</h4>
                                        <p class="text-gray-400 mb-2">Professional assistance</p>
                                        <a href="mailto:<EMAIL>" class="text-blue-400 hover:text-blue-300 font-medium"><EMAIL></a>
                                        <p class="text-sm text-gray-500 mt-1">Response within 15 minutes</p>
                                    </div>
                                </div>
                                
                                <div class="flex items-start space-x-4">
                                    <div class="w-12 h-12 bg-gradient-to-r from-purple-400 to-violet-500 rounded-lg flex items-center justify-center flex-shrink-0">
                                        <i data-lucide="message-circle" class="w-6 h-6 text-white"></i>
                                    </div>
                                    <div>
                                        <h4 class="text-lg font-semibold text-white">Live Chat</h4>
                                        <p class="text-gray-400 mb-2">Instant assistance</p>
                                        <button class="text-blue-400 hover:text-blue-300 font-medium" onclick="openLiveChat()">Start Chat Now</button>
                                        <p class="text-sm text-gray-500 mt-1">Available 24/7</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Sales Team -->
                        <div class="bg-slate-900/50 backdrop-blur-sm rounded-2xl p-8 border border-slate-800/50">
                            <h3 class="text-2xl font-bold text-white mb-6">Sales Team</h3>
                            
                            <div class="space-y-4">
        
                                
                                <div class="flex items-center space-x-4">
                                    <div class="w-10 h-10 bg-gradient-to-r from-pink-400 to-rose-500 rounded-lg flex items-center justify-center">
                                        <i data-lucide="mail" class="w-5 h-5 text-white"></i>
                                    </div>
                                    <div>
                                        <p class="text-gray-400">Sales Email</p>
                                        <a href="mailto:<EMAIL>" class="text-blue-400 hover:text-blue-300 font-medium"><EMAIL></a>
                                    </div>
                                </div>
                                
                                <div class="bg-slate-800/30 rounded-lg p-4 mt-6">
                                    <p class="text-sm text-gray-400">
                                        <i data-lucide="clock" class="w-4 h-4 inline mr-2"></i>
                                        Sales Hours: Monday - Friday, 9:00 - 18:00 (CET)
                                    </p>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Office Address -->
                        <div class="bg-slate-900/50 backdrop-blur-sm rounded-2xl p-8 border border-slate-800/50">
                            <h3 class="text-2xl font-bold text-white mb-6">Our Office</h3>
                            
                            <div class="flex items-start space-x-4">
                                <div class="w-12 h-12 bg-gradient-to-r from-indigo-400 to-blue-500 rounded-lg flex items-center justify-center flex-shrink-0">
                                    <i data-lucide="map-pin" class="w-6 h-6 text-white"></i>
                                </div>
                                <div>
                                    <h4 class="text-lg font-semibold text-white mb-2">X-Zone IT S.R.L.</h4>
                                    <address class="text-gray-400 not-italic leading-relaxed">
                                        57, Bulevardul 1 Mai<br>
                                        Mihai Voda, Bolitin Deal<br>
                                        Giurgiu, Romania
                                    </address>

                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        
 
    </main>

    <!-- Footer Placeholder -->
    <div id="footer-placeholder"></div>

    <!-- Scripts -->
    <script src="js/header-loader.js"></script>
    <script src="js/footer-loader.js"></script>
    <script src="js/advanced-performance-monitor.js"></script>
    <script src="js/next-gen-core-web-vitals.js"></script>
    <script src="js/semantic-html-enhancer.js"></script>
    <script src="js/google-undisclosed-ranking-factors.js"></script>
    
    <!-- Contact Form Handler -->
    <script>
        // Contact form validation and submission with WHMCS integration
        document.getElementById('contact-form').addEventListener('submit', function(e) {
            e.preventDefault();
            
            // Basic validation
            const requiredFields = ['first-name', 'last-name', 'email', 'subject', 'message'];
            let isValid = true;
            
            requiredFields.forEach(fieldId => {
                const field = document.getElementById(fieldId);
                if (!field.value.trim()) {
                    field.classList.add('border-red-500');
                    isValid = false;
                } else {
                    field.classList.remove('border-red-500');
                }
            });
            
            // Privacy consent check
            const privacyConsent = document.getElementById('privacy-consent');
            if (!privacyConsent.checked) {
                isValid = false;
                showNotification('Please agree to the privacy policy to continue.', 'error');
                return;
            }
            
            if (isValid) {
                const submitButton = document.querySelector('button[type="submit"]');
                const originalText = submitButton.innerHTML;
                
                // Show loading state
                submitButton.innerHTML = '<i data-lucide="loader-2" class="w-4 h-4 mr-2 inline animate-spin"></i>Sending...';
                submitButton.disabled = true;
                
                // Prepare form data
                const formData = {
                    first_name: document.getElementById('first-name').value.trim(),
                    last_name: document.getElementById('last-name').value.trim(),
                    email: document.getElementById('email').value.trim(),
                    phone: document.getElementById('phone').value.trim(),
                    subject: document.getElementById('subject').value,
                    message: document.getElementById('message').value.trim()
                };
                
                // Send to WHMCS ticket handler
                fetch('whmcs-ticket-handler.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(formData)
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Success - show ticket number
                        submitButton.innerHTML = '<i data-lucide="check" class="w-4 h-4 mr-2 inline"></i>Ticket Created!';
                        submitButton.classList.add('bg-green-600');
                        
                        showNotification(
                            `Success! ${data.message}`, 
                            'success'
                        );
                        
                        // Reset form after 5 seconds
                        setTimeout(() => {
                            document.getElementById('contact-form').reset();
                            submitButton.innerHTML = originalText;
                            submitButton.classList.remove('bg-green-600');
                            submitButton.disabled = false;
                            lucide.createIcons();
                        }, 5000);
                    } else {
                        // Error handling
                        throw new Error(data.error || 'Unknown error occurred');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    showNotification(
                        'There was an error sending your message. Please try again or contact us <NAME_EMAIL>', 
                        'error'
                    );
                    
                    // Reset button
                    submitButton.innerHTML = originalText;
                    submitButton.disabled = false;
                    lucide.createIcons();
                });
            }
        });
        
        // Notification system
        function showNotification(message, type = 'info') {
            // Remove existing notifications
            const existingNotifications = document.querySelectorAll('.notification');
            existingNotifications.forEach(notification => notification.remove());
            
            // Create notification element
            const notification = document.createElement('div');
            notification.className = `notification fixed top-4 right-4 max-w-md p-4 rounded-lg shadow-lg z-50 transform transition-all duration-300`;
            
            // Set colors based on type
            if (type === 'success') {
                notification.className += ' bg-green-600 text-white';
            } else if (type === 'error') {
                notification.className += ' bg-red-600 text-white';
            } else {
                notification.className += ' bg-blue-600 text-white';
            }
            
            notification.innerHTML = `
                <div class="flex items-center">
                    <div class="mr-3">
                        ${type === 'success' ? '<i data-lucide="check-circle" class="w-5 h-5"></i>' : 
                          type === 'error' ? '<i data-lucide="x-circle" class="w-5 h-5"></i>' : 
                          '<i data-lucide="info" class="w-5 h-5"></i>'}
                    </div>
                    <div class="flex-1">${message}</div>
                    <button onclick="this.parentElement.parentElement.remove()" class="ml-2 hover:opacity-80">
                        <i data-lucide="x" class="w-4 h-4"></i>
                    </button>
                </div>
            `;
            
            // Add to page
            document.body.appendChild(notification);
            
            // Initialize icons in notification
            if (typeof lucide !== 'undefined') {
                lucide.createIcons();
            }
            
            // Auto remove after 8 seconds for non-error messages
            if (type !== 'error') {
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.style.opacity = '0';
                        notification.style.transform = 'translateX(100%)';
                        setTimeout(() => notification.remove(), 300);
                    }
                }, 8000);
            }
        }
        
        // Live chat function
        function openLiveChat() {
            // This would typically open a live chat widget
            showNotification('Live chat feature would be integrated here with your preferred chat service (Intercom, Zendesk, etc.)', 'info');
        }
        
        // Initialize icons
        document.addEventListener('DOMContentLoaded', function() {
            if (typeof lucide !== 'undefined') {
                lucide.createIcons();
            }
        });
    </script>
</body>
</html>